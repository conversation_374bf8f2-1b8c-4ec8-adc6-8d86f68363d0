'use client';

import { useEffect, useState } from 'react';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useCustomerOAuth, storeOAuthSession } from '@/lib/hooks/useCustomerOAuth';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

export default function AuthCallbackPage() {
  const { handleOAuthCallback, isLoading: isOAuthLoading, error: oauthError } = useCustomerOAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        setIsProcessing(true);
        setError(null);

        // Get OAuth parameters from URL
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const oauthError = searchParams.get('error');

        console.log('🔄 Processing OAuth callback:', {
          hasCode: !!code,
          hasState: !!state,
          hasError: !!oauthError
        });

        // Handle OAuth error
        if (oauthError) {
          throw new Error(`OAuth error: ${oauthError}`);
        }

        // Validate required parameters
        if (!code || !state) {
          throw new Error('Missing OAuth parameters');
        }

        // Handle OAuth callback using new hook
        const result = await handleOAuthCallback(code, state);

        if (result.success && result.tokens && result.customer) {
          // Store tokens and customer data using the OAuth helper
          await storeOAuthSession(result.tokens, result.customer);

          console.log('✅ OAuth callback completed successfully:', {
            customerId: result.customer.id,
            email: result.customer.email,
            isNewCustomer: result.isNewCustomer
          });

          setIsSuccess(true);
          toast.success(result.isNewCustomer ? 'Account created successfully!' : 'Welcome back!');

          // Redirect to dashboard after short delay
          setTimeout(() => {
            router.push('/');
          }, 2000);
        } else {
          throw new Error('Invalid OAuth callback response');
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'OAuth callback failed';
        console.error('❌ OAuth callback error:', error);
        setError(errorMessage);
        toast.error(`Authentication failed: ${errorMessage}`);

        // Redirect to sign-in page after delay
        setTimeout(() => {
          router.push(`/auth/signin?error=${encodeURIComponent(errorMessage)}`);
        }, 3000);
      } finally {
        setIsProcessing(false);
      }
    };

    handleCallback();
  }, [searchParams, handleOAuthCallback, router]);

  const handleRetry = () => {
    router.push('/auth/signin');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              {isProcessing && (
                <Loader2 className="h-12 w-12 animate-spin text-teal-600" />
              )}
              {!isProcessing && !error && (
                <CheckCircle className="h-12 w-12 text-green-600" />
              )}
              {error && (
                <XCircle className="h-12 w-12 text-red-600" />
              )}
            </div>
            
            <CardTitle className="text-2xl">
              {isProcessing && 'Completing sign in...'}
              {isSuccess && 'Sign in successful!'}
              {error && 'Sign in failed'}
            </CardTitle>

            <CardDescription>
              {isProcessing && 'Please wait while we complete your authentication.'}
              {isSuccess && 'You have been successfully signed in. Redirecting...'}
              {error && 'There was a problem with your authentication.'}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {isProcessing && (
              <div className="text-center">
                <div className="space-y-2">
                  <div className="text-sm text-gray-600">
                    Verifying your Google account...
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-teal-600 h-2 rounded-full animate-pulse w-3/4"></div>
                  </div>
                </div>
              </div>
            )}

            {isSuccess && (
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center space-x-2">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  <span className="text-green-600">Authentication completed successfully!</span>
                </div>
                <div className="text-sm text-gray-600">
                  Redirecting you to your dashboard...
                </div>
                <Button
                  onClick={() => router.push('/')}
                  className="w-full bg-teal-600 hover:bg-teal-700"
                >
                  Continue to Dashboard
                </Button>
              </div>
            )}

            {error && (
              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-sm text-red-800">
                    <strong>Error:</strong> {error}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Button
                    onClick={() => router.push('/auth/signin')}
                    className="w-full bg-teal-600 hover:bg-teal-700"
                  >
                    Try Again
                  </Button>

                  <Button
                    onClick={() => router.push('/')}
                    variant="outline"
                    className="w-full"
                  >
                    Go to Home
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help text */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Having trouble signing in?{' '}
            <a 
              href="/support" 
              className="text-teal-600 hover:text-teal-500"
            >
              Contact support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
