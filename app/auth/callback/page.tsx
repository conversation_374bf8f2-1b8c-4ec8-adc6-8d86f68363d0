'use client';

import { useEffect } from 'react';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useGoogleOAuthCallback } from '@/lib/hooks/useAuthentication';
import { useRouter } from 'next/navigation';

export default function AuthCallbackPage() {
  const { isProcessing, error, handleCallback } = useGoogleOAuthCallback();
  const router = useRouter();

  useEffect(() => {
    // Handle the OAuth callback when component mounts
    handleCallback();
  }, [handleCallback]);

  const handleRetry = () => {
    router.push('/auth/signin');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              {isProcessing && (
                <Loader2 className="h-12 w-12 animate-spin text-teal-600" />
              )}
              {!isProcessing && !error && (
                <CheckCircle className="h-12 w-12 text-green-600" />
              )}
              {error && (
                <XCircle className="h-12 w-12 text-red-600" />
              )}
            </div>
            
            <CardTitle className="text-2xl">
              {isProcessing && 'Completing sign in...'}
              {!isProcessing && !error && 'Sign in successful!'}
              {error && 'Sign in failed'}
            </CardTitle>
            
            <CardDescription>
              {isProcessing && 'Please wait while we complete your authentication.'}
              {!isProcessing && !error && 'You have been successfully signed in.'}
              {error && 'There was a problem with your authentication.'}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {isProcessing && (
              <div className="text-center">
                <div className="space-y-2">
                  <div className="text-sm text-gray-600">
                    Verifying your Google account...
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-teal-600 h-2 rounded-full animate-pulse w-3/4"></div>
                  </div>
                </div>
              </div>
            )}

            {!isProcessing && !error && (
              <div className="text-center space-y-4">
                <div className="text-sm text-gray-600">
                  Redirecting you to your dashboard...
                </div>
                <Button 
                  onClick={handleGoHome}
                  className="w-full bg-teal-600 hover:bg-teal-700"
                >
                  Continue to Dashboard
                </Button>
              </div>
            )}

            {error && (
              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-sm text-red-800">
                    <strong>Error:</strong> {error}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Button 
                    onClick={handleRetry}
                    className="w-full bg-teal-600 hover:bg-teal-700"
                  >
                    Try Again
                  </Button>
                  
                  <Button 
                    onClick={handleGoHome}
                    variant="outline"
                    className="w-full"
                  >
                    Go to Home
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help text */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Having trouble signing in?{' '}
            <a 
              href="/support" 
              className="text-teal-600 hover:text-teal-500"
            >
              Contact support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
