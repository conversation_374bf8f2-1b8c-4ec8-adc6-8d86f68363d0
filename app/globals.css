@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 25 5% 15%;
    --card: 0 0% 100%;
    --card-foreground: 25 5% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 25 5% 15%;
    --primary: 142 76% 36%;
    --primary-foreground: 355 7% 97%;
    --secondary: 25 5% 96%;
    --secondary-foreground: 25 9% 10%;
    --muted: 25 5% 96%;
    --muted-foreground: 25 5% 45%;
    --accent: 25 5% 96%;
    --accent-foreground: 25 9% 10%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 355 7% 97%;
    --border: 25 6% 89%;
    --input: 25 6% 89%;
    --ring: 142 76% 36%;
    --radius: 1rem;

    /* Custom sage colors */
    --sage-50: 138 20% 97%;
    --sage-100: 138 15% 94%;
    --sage-200: 138 12% 87%;
    --sage-300: 138 10% 76%;
    --sage-400: 138 8% 64%;
    --sage-500: 138 10% 53%;
    --sage-600: 142 76% 36%;
    --sage-700: 142 85% 28%;
    --sage-800: 142 90% 22%;
    --sage-900: 142 95% 18%;
  }

  .dark {
    --background: 25 9% 4%;
    --foreground: 355 7% 97%;
    --card: 25 9% 4%;
    --card-foreground: 355 7% 97%;
    --popover: 25 9% 4%;
    --popover-foreground: 355 7% 97%;
    --primary: 142 76% 36%;
    --primary-foreground: 25 9% 4%;
    --secondary: 25 5% 15%;
    --secondary-foreground: 355 7% 97%;
    --muted: 25 5% 15%;
    --muted-foreground: 25 5% 64%;
    --accent: 25 5% 15%;
    --accent-foreground: 355 7% 97%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 355 7% 97%;
    --border: 25 5% 15%;
    --input: 25 5% 15%;
    --ring: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Focus styles */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Animation utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading skeleton */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Add sage color utilities */
.bg-sage-50 {
  background-color: hsl(var(--sage-50));
}
.bg-sage-100 {
  background-color: hsl(var(--sage-100));
}
.bg-sage-200 {
  background-color: hsl(var(--sage-200));
}
.bg-sage-300 {
  background-color: hsl(var(--sage-300));
}
.bg-sage-400 {
  background-color: hsl(var(--sage-400));
}
.bg-sage-500 {
  background-color: hsl(var(--sage-500));
}
.bg-sage-600 {
  background-color: hsl(var(--sage-600));
}
.bg-sage-700 {
  background-color: hsl(var(--sage-700));
}
.bg-sage-800 {
  background-color: hsl(var(--sage-800));
}
.bg-sage-900 {
  background-color: hsl(var(--sage-900));
}

.text-sage-50 {
  color: hsl(var(--sage-50));
}
.text-sage-100 {
  color: hsl(var(--sage-100));
}
.text-sage-200 {
  color: hsl(var(--sage-200));
}
.text-sage-300 {
  color: hsl(var(--sage-300));
}
.text-sage-400 {
  color: hsl(var(--sage-400));
}
.text-sage-500 {
  color: hsl(var(--sage-500));
}
.text-sage-600 {
  color: hsl(var(--sage-600));
}
.text-sage-700 {
  color: hsl(var(--sage-700));
}
.text-sage-800 {
  color: hsl(var(--sage-800));
}
.text-sage-900 {
  color: hsl(var(--sage-900));
}

.border-sage-50 {
  border-color: hsl(var(--sage-50));
}
.border-sage-100 {
  border-color: hsl(var(--sage-100));
}
.border-sage-200 {
  border-color: hsl(var(--sage-200));
}
.border-sage-300 {
  border-color: hsl(var(--sage-300));
}
.border-sage-400 {
  border-color: hsl(var(--sage-400));
}
.border-sage-500 {
  border-color: hsl(var(--sage-500));
}
.border-sage-600 {
  border-color: hsl(var(--sage-600));
}
.border-sage-700 {
  border-color: hsl(var(--sage-700));
}
.border-sage-800 {
  border-color: hsl(var(--sage-800));
}
.border-sage-900 {
  border-color: hsl(var(--sage-900));
}

/* Enhanced wellness-focused styling */
.wellness-gradient {
  background: linear-gradient(135deg, hsl(var(--sage-50)) 0%, hsl(var(--sage-100)) 50%, hsl(25 5% 96%) 100%);
}

.glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Soft shadows for wellness aesthetic */
.wellness-shadow {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.02);
}

.wellness-shadow-lg {
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.08), 0 8px 16px -4px rgba(0, 0, 0, 0.04);
}

/* Smooth, organic transitions */
.organic-transition {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Typography enhancements for wellness brand */
.wellness-heading {
  font-weight: 300;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.wellness-body {
  font-weight: 400;
  line-height: 1.6;
  color: hsl(25 5% 45%);
}
