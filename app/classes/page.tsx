// 📚 Halaman Classes - Public Classes Integration
// Implementasi sesuai dokumentasi public-classes-frontend-integration.md

'use client';

import React, { useState } from 'react';
import { ClassesList } from '@/components/public/ClassesList';
import { PublicClassDTO } from '@/types/public-api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  Settings,
  BookOpen,
  Users,
  Calendar
} from 'lucide-react';

export default function ClassesPage() {
  const [selectedClass, setSelectedClass] = useState<PublicClassDTO | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTenant, setSelectedTenant] = useState('1');
  const [showFilters, setShowFilters] = useState(false);

  const handleClassSelect = (classItem: PublicClassDTO) => {
    setSelectedClass(classItem);
    console.log('Selected class:', classItem);
  };

  const handleCloseDetail = () => {
    setSelectedClass(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <BookOpen className="h-8 w-8 text-blue-600" />
                  <h1 className="text-3xl font-bold text-gray-900">Classes</h1>
                </div>
                <Badge variant="outline" className="text-sm">
                  Public API Integration
                </Badge>
              </div>
              
              <div className="flex items-center space-x-3">
                {/* View Mode Toggle */}
                <div className="flex items-center border rounded-lg p-1">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="h-8 w-8 p-0"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="h-8 w-8 p-0"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>

                {/* Filters Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="mt-6 space-y-4">
              <div className="flex flex-col sm:flex-row gap-4">
                {/* Search */}
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Cari classes..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Tenant Selection */}
                <div className="w-full sm:w-48">
                  <Select value={selectedTenant} onValueChange={setSelectedTenant}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih Tenant" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Tenant 1</SelectItem>
                      <SelectItem value="2">Tenant 2</SelectItem>
                      <SelectItem value="3">Tenant 3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Settings className="h-5 w-5 mr-2" />
                      Filter Lanjutan
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                          Kategori
                        </label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Semua Kategori" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">Semua Kategori</SelectItem>
                            <SelectItem value="fitness">Fitness</SelectItem>
                            <SelectItem value="yoga">Yoga</SelectItem>
                            <SelectItem value="pilates">Pilates</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                          Status
                        </label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Semua Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">Semua Status</SelectItem>
                            <SelectItem value="active">Aktif</SelectItem>
                            <SelectItem value="inactive">Tidak Aktif</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                          Tanggal
                        </label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Semua Tanggal" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">Semua Tanggal</SelectItem>
                            <SelectItem value="today">Hari Ini</SelectItem>
                            <SelectItem value="week">Minggu Ini</SelectItem>
                            <SelectItem value="month">Bulan Ini</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {selectedClass ? (
          /* Class Detail View */
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <Button variant="outline" onClick={handleCloseDetail}>
                ← Kembali ke Daftar
              </Button>
            </div>
            
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-2xl">{selectedClass.name}</CardTitle>
                    <p className="text-gray-600 mt-2">{selectedClass.description}</p>
                  </div>
                  <Badge 
                    variant={selectedClass.isActive ? 'default' : 'secondary'}
                    className={selectedClass.isActive ? 'bg-green-100 text-green-800' : ''}
                  >
                    {selectedClass.isActive ? 'Aktif' : 'Tidak Aktif'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Detail Class</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">ID:</span>
                          <span className="font-mono">{selectedClass.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Kategori:</span>
                          <span>{selectedClass.category}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Mulai:</span>
                          <span>{new Date(selectedClass.startDate).toLocaleString('id-ID')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Selesai:</span>
                          <span>{new Date(selectedClass.endDate).toLocaleString('id-ID')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Informasi Sistem</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Dibuat:</span>
                          <span>{new Date(selectedClass.createdAt).toLocaleString('id-ID')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Diupdate:</span>
                          <span>{new Date(selectedClass.updatedAt).toLocaleString('id-ID')}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="pt-4">
                      <Button className="w-full">
                        <Users className="h-4 w-4 mr-2" />
                        Book Class
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          /* Classes List View */
          <ClassesList
            tenantId={selectedTenant}
            limit={12}
            showPagination={true}
            showFilters={false}
            onClassSelect={handleClassSelect}
          />
        )}
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span>© 2024 Classes App</span>
              <span>•</span>
              <span>Public API Integration</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Powered by Next.js & TanStack Query</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
