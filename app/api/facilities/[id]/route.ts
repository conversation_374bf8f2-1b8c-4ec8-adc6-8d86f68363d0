import { NextRequest, NextResponse } from 'next/server';

const API_KEY = process.env.PACKAGE_PRICING_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';
const BASE_API_URL = 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    };

    console.log('🔍 Fetching facility details for ID:', id);

    const response = await fetch(`${BASE_API_URL}/api/public/facilities/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      // Return fallback facility names
      const facilityNames = [
        'Reformer Machine', 'Mat Area', 'Cadillac', 'Chair', 
        'Barrel', 'Tower', 'Springboard', 'Props Area'
      ];
      const hash = id.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
      const facilityName = facilityNames[hash % facilityNames.length];

      return NextResponse.json(
        { 
          success: false, 
          error: 'Facility not found',
          fallback: true,
          data: {
            id,
            name: facilityName,
            description: 'Facility details not available',
            capacity: 10,
            is_active: true
          }
        },
        { status: 200, headers }
      );
    }

    const data = await response.json();
    console.log('✅ Facility data fetched successfully:', { id, name: data.data?.name });
    
    return NextResponse.json(
      {
        success: true,
        data: data.data || data,
      },
      { status: 200, headers }
    );

  } catch (error) {
    console.error('Facility API proxy error:', error);
    
    // Return fallback data
    const { id } = params;
    const facilityNames = [
      'Reformer Machine', 'Mat Area', 'Cadillac', 'Chair', 
      'Barrel', 'Tower', 'Springboard', 'Props Area'
    ];
    const hash = id.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    const facilityName = facilityNames[hash % facilityNames.length];

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while fetching facility',
        fallback: true,
        data: {
          id,
          name: facilityName,
          description: 'Facility details not available',
          capacity: 10,
          is_active: true
        }
      },
      { status: 200 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
