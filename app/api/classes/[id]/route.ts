import { NextRequest, NextResponse } from 'next/server';

// API key stored securely in backend environment
const API_KEY = process.env.PACKAGE_PRICING_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';
const BASE_API_URL = 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Add CORS headers for development
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    };

    console.log('🔍 Fetching class details for ID:', id);

    // Fetch class data from the external API
    const response = await fetch(`${BASE_API_URL}/api/public/classes/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      cache: 'no-store', // Ensure fresh data
    });

    console.log('📡 Class API response:', {
      status: response.status,
      ok: response.ok,
      statusText: response.statusText
    });

    if (!response.ok) {
      console.error('Class API error:', response.status, response.statusText);
      
      // If class not found, return a fallback response
      if (response.status === 404) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Class not found',
            fallback: true,
            data: {
              id,
              name: `Class ${id.slice(-8)}`,
              description: 'Class details not available',
              class_type: 'group',
              level: null,
              duration: 60,
              max_capacity: 10,
              price: 0,
              is_active: true
            }
          },
          { status: 200, headers } // Return 200 with fallback data
        );
      }

      return NextResponse.json(
        { 
          success: false, 
          error: `Failed to fetch class: ${response.statusText}`,
          status: response.status 
        },
        { status: response.status, headers }
      );
    }

    const data = await response.json();
    console.log('✅ Class data fetched successfully:', { id, name: data.data?.name });
    
    // Return the data with success wrapper
    return NextResponse.json(
      {
        success: true,
        data: data.data || data,
      },
      { status: 200, headers }
    );

  } catch (error) {
    console.error('Class API proxy error:', error);
    
    // Return fallback data on error
    const { id } = params;
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while fetching class',
        fallback: true,
        data: {
          id,
          name: `Class ${id.slice(-8)}`,
          description: 'Class details not available',
          class_type: 'group',
          level: null,
          duration: 60,
          max_capacity: 10,
          price: 0,
          is_active: true
        },
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 200 } // Return 200 with fallback data
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
