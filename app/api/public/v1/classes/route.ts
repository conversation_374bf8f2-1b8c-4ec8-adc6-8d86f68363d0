// 🚀 Public Classes API Route
// Implementasi endpoint /api/public/v1/classes sesuai dokumentasi

import { NextRequest, NextResponse } from 'next/server';
import { PublicClassDTO, PublicClassesResponse, PublicClassResponse } from '@/types/public-api';

// API key stored securely in backend environment
const API_KEY = process.env.PACKAGE_PRICING_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';

// Mock data untuk development - nanti akan diganti dengan database real
const mockClasses: PublicClassDTO[] = [
  {
    id: 'cls_abc123',
    name: 'Yoga Pemula',
    description: 'Kelas yoga untuk pemula yang ingin belajar dasar-dasar yoga dengan instruktur berpengalaman',
    category: 'Fitness',
    startDate: '2024-01-15T09:00:00.000Z',
    endDate: '2024-01-15T10:30:00.000Z',
    isActive: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  {
    id: 'cls_def456',
    name: 'Pilates Intermediate',
    description: 'Kelas pilates tingkat menengah untuk meningkatkan kekuatan core dan fleksibilitas',
    category: 'Pilates',
    startDate: '2024-01-16T14:00:00.000Z',
    endDate: '2024-01-16T15:00:00.000Z',
    isActive: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z'
  },
  {
    id: 'cls_ghi789',
    name: 'HIIT Training',
    description: 'High Intensity Interval Training untuk membakar kalori dan meningkatkan stamina',
    category: 'Fitness',
    startDate: '2024-01-17T18:00:00.000Z',
    endDate: '2024-01-17T19:00:00.000Z',
    isActive: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  {
    id: 'cls_jkl012',
    name: 'Meditation & Mindfulness',
    description: 'Sesi meditasi dan mindfulness untuk ketenangan pikiran dan mengurangi stress',
    category: 'Wellness',
    startDate: '2024-01-18T07:00:00.000Z',
    endDate: '2024-01-18T08:00:00.000Z',
    isActive: false,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-03T00:00:00.000Z'
  },
  {
    id: 'cls_mno345',
    name: 'Strength Training',
    description: 'Latihan kekuatan dengan beban untuk membangun massa otot dan meningkatkan metabolisme',
    category: 'Fitness',
    startDate: '2024-01-19T16:00:00.000Z',
    endDate: '2024-01-19T17:30:00.000Z',
    isActive: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }
];

// Validate API key
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-api-key');
  return apiKey === API_KEY;
}

// Add CORS headers
function addCorsHeaders(response: NextResponse): NextResponse {
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-api-key');
  return response;
}

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Public Classes API called');

    // Validate API key
    if (!validateApiKey(request)) {
      console.log('❌ Invalid API key');
      const response = NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
      return addCorsHeaders(response);
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const id = searchParams.get('id');

    console.log('📋 Query params:', { tenantId, limit, page, id });

    // If ID is provided, return single class
    if (id) {
      const classItem = mockClasses.find(c => c.id === id);
      
      if (!classItem) {
        console.log('❌ Class not found:', id);
        const response = NextResponse.json(
          { success: false, error: 'Class not found' },
          { status: 404 }
        );
        return addCorsHeaders(response);
      }

      console.log('✅ Single class found:', classItem.name);
      const response = NextResponse.json({
        success: true,
        data: classItem
      } as PublicClassResponse);
      
      return addCorsHeaders(response);
    }

    // Filter classes by tenant if provided
    let filteredClasses = mockClasses;
    if (tenantId) {
      // In real implementation, filter by tenantId from database
      // For now, we'll just use all classes
      console.log('🏢 Filtering by tenant:', tenantId);
    }

    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedClasses = filteredClasses.slice(startIndex, endIndex);
    const total = filteredClasses.length;
    const pageCount = Math.ceil(total / limit);

    console.log('📊 Pagination:', { 
      total, 
      page, 
      limit, 
      pageCount, 
      returned: paginatedClasses.length 
    });

    // Return paginated response
    const response = NextResponse.json({
      success: true,
      data: {
        classes: paginatedClasses
      },
      meta: {
        total,
        page,
        limit,
        pageCount
      }
    } as PublicClassesResponse);

    console.log('✅ Classes API success');
    return addCorsHeaders(response);

  } catch (error) {
    console.error('❌ Public Classes API error:', error);
    
    const response = NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while fetching classes',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
    
    return addCorsHeaders(response);
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  console.log('🔄 CORS preflight request');
  
  const response = new NextResponse(null, { status: 200 });
  return addCorsHeaders(response);
}

// Health check endpoint
export async function HEAD() {
  console.log('🏥 Health check request');
  
  const response = new NextResponse(null, { status: 200 });
  response.headers.set('X-API-Status', 'healthy');
  response.headers.set('X-API-Version', '1.0.0');
  
  return addCorsHeaders(response);
}
