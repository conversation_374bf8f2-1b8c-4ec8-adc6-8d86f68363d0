import { NextRequest, NextResponse } from 'next/server';
import { Customer } from '@/types/auth';

// External API configuration
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';
const EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY || '';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        {
          success: false,
          error: 'No authorization token provided',
          errorCode: 'TOKEN_INVALID'
        },
        { status: 401 }
      );
    }

    console.log('🔍 Fetching customer profile');

    // Call external API to get customer profile
    const externalProfileResponse = await fetch(`${EXTERNAL_API_BASE_URL}/api/auth/customer/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
        'X-API-Key': EXTERNAL_API_KEY,
      },
    });

    const responseText = await externalProfileResponse.text();
    
    if (!externalProfileResponse.ok) {
      console.error('External API profile fetch failed:', {
        status: externalProfileResponse.status,
        statusText: externalProfileResponse.statusText,
        response: responseText
      });

      // Parse error response if possible
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch {
        errorData = { error: 'Profile fetch failed' };
      }

      // Map common error codes
      let errorCode = 'TOKEN_INVALID';
      if (externalProfileResponse.status === 401) {
        errorCode = 'TOKEN_EXPIRED';
      } else if (externalProfileResponse.status === 404) {
        errorCode = 'CUSTOMER_NOT_FOUND';
      } else if (errorData.errorCode) {
        errorCode = errorData.errorCode;
      }
      
      return NextResponse.json(
        {
          success: false,
          error: errorData.error || 'Profile fetch failed',
          errorCode
        },
        { status: externalProfileResponse.status }
      );
    }

    const profileResult = JSON.parse(responseText);
    console.log('✅ External API profile fetch successful:', { 
      customerId: profileResult.id,
      email: profileResult.email 
    });

    // Create customer object
    const customer: Customer = {
      id: profileResult.id,
      email: profileResult.email,
      firstName: profileResult.firstName,
      lastName: profileResult.lastName,
      displayName: profileResult.displayName,
      image: profileResult.image,
      tenantId: profileResult.tenantId,
      membershipType: profileResult.membershipType,
      isEmailVerified: profileResult.isEmailVerified,
      createdAt: profileResult.createdAt,
      updatedAt: profileResult.updatedAt,
    };

    console.log('🎉 Customer profile fetch completed successfully');

    return NextResponse.json(customer, { status: 200 });

  } catch (error) {
    console.error('Customer profile fetch error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Profile fetch failed',
        errorCode: 'TOKEN_INVALID'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
