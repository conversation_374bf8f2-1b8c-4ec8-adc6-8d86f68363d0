import { NextRequest, NextResponse } from 'next/server';

// External API configuration
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';
const EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY || '';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    
    console.log('🔍 Processing customer logout:', {
      hasAuthHeader: !!authHeader
    });

    // If we have an auth header, try to revoke the session on the external API
    if (authHeader) {
      try {
        const externalLogoutResponse = await fetch(`${EXTERNAL_API_BASE_URL}/api/auth/customer/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': authHeader,
            'X-API-Key': EXTERNAL_API_KEY,
          },
        });

        if (externalLogoutResponse.ok) {
          console.log('✅ External API logout successful');
        } else {
          console.warn('⚠️ External API logout failed, but continuing with local logout');
        }
      } catch (error) {
        console.warn('⚠️ External API logout error, but continuing with local logout:', error);
      }
    }

    // Always return success for logout to ensure client-side cleanup happens
    const response = {
      success: true,
      message: 'Logged out successfully'
    };

    console.log('🎉 Customer logout completed');

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Customer logout error:', error);
    
    // Even if there's an error, we should return success for logout
    // to ensure the client clears its tokens
    return NextResponse.json(
      {
        success: true,
        message: 'Logged out successfully'
      },
      { status: 200 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
