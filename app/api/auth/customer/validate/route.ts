import { NextRequest, NextResponse } from 'next/server';
import { Customer } from '@/types/auth';

// External API configuration
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';
const EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY || '';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        {
          valid: false,
          error: 'No authorization token provided'
        },
        { status: 401 }
      );
    }

    console.log('🔍 Validating customer session');

    // Call external API to validate the session
    const externalValidateResponse = await fetch(`${EXTERNAL_API_BASE_URL}/api/auth/customer/validate`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
        'X-API-Key': EXTERNAL_API_KEY,
      },
    });

    const responseText = await externalValidateResponse.text();
    
    if (!externalValidateResponse.ok) {
      console.warn('External API session validation failed:', {
        status: externalValidateResponse.status,
        statusText: externalValidateResponse.statusText
      });
      
      return NextResponse.json(
        {
          valid: false,
          error: 'Session validation failed'
        },
        { status: 401 }
      );
    }

    const validationResult = JSON.parse(responseText);
    
    if (!validationResult.valid) {
      console.warn('Session is not valid according to external API');
      
      return NextResponse.json(
        {
          valid: false,
          error: 'Session is not valid'
        },
        { status: 401 }
      );
    }

    console.log('✅ Session validation successful:', { 
      customerId: validationResult.customer?.id 
    });

    // Create customer object if provided
    let customer: Customer | undefined;
    if (validationResult.customer) {
      customer = {
        id: validationResult.customer.id,
        email: validationResult.customer.email,
        firstName: validationResult.customer.firstName,
        lastName: validationResult.customer.lastName,
        displayName: validationResult.customer.displayName,
        image: validationResult.customer.image,
        tenantId: validationResult.customer.tenantId,
        membershipType: validationResult.customer.membershipType,
        isEmailVerified: validationResult.customer.isEmailVerified,
        createdAt: validationResult.customer.createdAt,
        updatedAt: validationResult.customer.updatedAt,
      };
    }

    const response = {
      valid: true,
      customer
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Session validation error:', error);
    
    return NextResponse.json(
      {
        valid: false,
        error: 'Session validation failed'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
