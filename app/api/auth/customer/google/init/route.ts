import { NextRequest, NextResponse } from 'next/server';
import { GoogleOAuthInitRequest, GoogleOAuthInitResponse } from '@/types/auth';
import { generateSecureState, generateCodeVerifier, generateCodeChallenge } from '@/lib/utils/device';
import { getGoogleRedirectUri, logEnvironmentConfig } from '@/lib/utils/environment';

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || '';
const GOOGLE_REDIRECT_URI = getGoogleRedirectUri();

export async function POST(request: NextRequest) {
  try {
    const body: GoogleOAuthInitRequest = await request.json();
    
    // Validate required fields
    if (!body.tenantId || !body.deviceType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          errorCode: 'MISSING_REQUIRED_FIELD'
        },
        { status: 400 }
      );
    }

    // Generate secure state parameter
    const state = generateSecureState();
    
    // Generate PKCE challenge for mobile or if requested
    let codeChallenge: string | undefined;
    let codeChallengeMethod: string | undefined;
    
    if (body.deviceType === 'mobile' || body.codeChallenge) {
      if (body.codeChallenge) {
        // Use provided code challenge (for mobile)
        codeChallenge = body.codeChallenge;
        codeChallengeMethod = body.codeChallengeMethod || 'S256';
      } else {
        // Generate code challenge for web PKCE
        const codeVerifier = generateCodeVerifier();
        codeChallenge = await generateCodeChallenge(codeVerifier);
        codeChallengeMethod = 'S256';
        
        // Store code verifier in session/database for later verification
        // For now, we'll include it in the state (in production, store securely)
        console.log('Generated code verifier for web PKCE:', codeVerifier.substring(0, 10) + '...');
      }
    }

    // Build Google OAuth authorization URL
    const authParams = new URLSearchParams({
      client_id: GOOGLE_CLIENT_ID,
      redirect_uri: body.redirectUri || GOOGLE_REDIRECT_URI,
      response_type: 'code',
      scope: 'openid email profile',
      state: state,
      access_type: 'offline',
      prompt: 'consent',
    });

    // Add PKCE parameters if available
    if (codeChallenge && codeChallengeMethod) {
      authParams.append('code_challenge', codeChallenge);
      authParams.append('code_challenge_method', codeChallengeMethod);
    }

    const authorizationUrl = `https://accounts.google.com/o/oauth2/v2/auth?${authParams.toString()}`;

    // Log environment configuration for debugging
    logEnvironmentConfig();

    // Log the OAuth initialization
    console.log('🔐 Google OAuth initialized:', {
      tenantId: body.tenantId,
      deviceType: body.deviceType,
      deviceId: body.deviceId?.substring(0, 8) + '...',
      hasPKCE: !!codeChallenge,
      state: state.substring(0, 10) + '...',
      redirectUri: body.redirectUri || GOOGLE_REDIRECT_URI
    });

    const response: GoogleOAuthInitResponse = {
      success: true,
      authorizationUrl,
      state,
      codeChallenge: codeChallenge
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Google OAuth init error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to initialize Google OAuth',
        errorCode: 'GOOGLE_API_ERROR'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
