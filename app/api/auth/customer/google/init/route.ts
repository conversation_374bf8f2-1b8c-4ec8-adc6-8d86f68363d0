import { NextRequest, NextResponse } from 'next/server';
import { GoogleOAuthInitRequest, GoogleOAuthInitResponse } from '@/types/auth';

// Backend API Configuration (Customer OAuth endpoints)
const BACKEND_API_URL = process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';

export async function POST(request: NextRequest) {
  try {
    const body: GoogleOAuthInitRequest = await request.json();

    console.log('🚀 Proxying OAuth init to backend:', {
      backendUrl: BACKEND_API_URL,
      hasDeviceId: !!body.deviceId,
      deviceType: body.deviceType,
      hasRedirectUri: !!body.redirectUri
    });

    // Proxy request to backend Customer OAuth init endpoint
    const backendResponse = await fetch(`${BACKEND_API_URL}/api/auth/customer/google/init`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // ❌ NO API KEY for OAuth endpoints
      },
      body: JSON.stringify({
        tenantId: body.tenantId || 1,
        clientType: body.deviceType || 'web', // Map deviceType to clientType
        deviceId: body.deviceId,
        redirectUri: body.redirectUri
      })
    });

    console.log('📊 Backend OAuth init response:', {
      status: backendResponse.status,
      statusText: backendResponse.statusText,
      contentType: backendResponse.headers.get('content-type')
    });

    if (!backendResponse.ok) {
      const errorData = await backendResponse.text();
      console.error('❌ Backend OAuth init failed:', errorData);

      return NextResponse.json(
        {
          success: false,
          error: 'OAuth initialization failed',
          errorCode: 'BACKEND_ERROR'
        },
        { status: backendResponse.status }
      );
    }

    const result = await backendResponse.json();

    console.log('✅ OAuth init successful:', {
      hasAuthUrl: !!result.authUrl,
      hasState: !!result.state,
      hasPKCE: !!result.codeVerifier
    });

    return NextResponse.json(result, { status: 200 });

  } catch (error) {
    console.error('❌ OAuth init proxy error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to initialize OAuth',
        errorCode: 'PROXY_ERROR'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
