import { NextRequest, NextResponse } from 'next/server';
import { GoogleOAuthCallbackRequest, GoogleOAuthCallbackResponse, Customer, TokenPair } from '@/types/auth';
import { getGoogleRedirectUri } from '@/lib/utils/environment';

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || '';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '';
const GOOGLE_REDIRECT_URI = getGoogleRedirectUri();

// External API configuration
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';
const EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY || '';

export async function POST(request: NextRequest) {
  try {
    const body: GoogleOAuthCallbackRequest = await request.json();
    
    // Validate required fields
    if (!body.code || !body.state || !body.deviceType) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          errorCode: 'MISSING_REQUIRED_FIELD'
        },
        { status: 400 }
      );
    }

    console.log('🔍 Processing Google OAuth callback:', {
      hasCode: !!body.code,
      hasState: !!body.state,
      deviceType: body.deviceType,
      deviceId: body.deviceId?.substring(0, 8) + '...'
    });

    // Debug Google OAuth configuration
    console.log('🔍 Google OAuth configuration:', {
      hasClientId: !!GOOGLE_CLIENT_ID,
      clientIdPrefix: GOOGLE_CLIENT_ID?.substring(0, 20) + '...',
      hasClientSecret: !!GOOGLE_CLIENT_SECRET,
      redirectUri: GOOGLE_REDIRECT_URI,
      codeLength: body.code?.length,
      hasCodeVerifier: !!body.codeVerifier
    });

    // Exchange authorization code for tokens
    const tokenParams = new URLSearchParams({
      client_id: GOOGLE_CLIENT_ID,
      client_secret: GOOGLE_CLIENT_SECRET,
      code: body.code,
      grant_type: 'authorization_code',
      redirect_uri: GOOGLE_REDIRECT_URI,
    });

    // Add PKCE code verifier if provided (for mobile)
    if (body.codeVerifier) {
      tokenParams.append('code_verifier', body.codeVerifier);
    }

    console.log('🔍 Token exchange request:', {
      url: 'https://oauth2.googleapis.com/token',
      params: {
        client_id: GOOGLE_CLIENT_ID?.substring(0, 20) + '...',
        grant_type: 'authorization_code',
        redirect_uri: GOOGLE_REDIRECT_URI,
        code: body.code?.substring(0, 20) + '...',
        hasCodeVerifier: !!body.codeVerifier
      }
    });

    // Exchange code for Google tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });

    console.log('🔍 Google token response:', {
      status: tokenResponse.status,
      statusText: tokenResponse.statusText,
      headers: Object.fromEntries(tokenResponse.headers.entries())
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.error('❌ Google token exchange failed:', {
        status: tokenResponse.status,
        statusText: tokenResponse.statusText,
        errorData: errorData,
        requestParams: {
          client_id: GOOGLE_CLIENT_ID?.substring(0, 20) + '...',
          redirect_uri: GOOGLE_REDIRECT_URI,
          code: body.code?.substring(0, 20) + '...'
        }
      });

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to exchange authorization code',
          errorCode: 'OAUTH_CODE_EXPIRED'
        },
        { status: 400 }
      );
    }

    const googleTokens = await tokenResponse.json();
    console.log('✅ Google tokens received:', { 
      hasAccessToken: !!googleTokens.access_token,
      hasRefreshToken: !!googleTokens.refresh_token 
    });

    // Get user info from Google
    const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${googleTokens.access_token}`,
      },
    });

    if (!userInfoResponse.ok) {
      console.error('Failed to get Google user info');
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to get user information',
          errorCode: 'GOOGLE_API_ERROR'
        },
        { status: 500 }
      );
    }

    const googleUser = await userInfoResponse.json();
    console.log('✅ Google user info received:', { 
      email: googleUser.email,
      name: googleUser.name 
    });

    // Authenticate with external API using Google OAuth
    const authRequest = {
      email: googleUser.email,
      firstName: googleUser.given_name || '',
      lastName: googleUser.family_name || '',
      displayName: googleUser.name || '',
      image: googleUser.picture || '',
      googleId: googleUser.id,
      googleAccessToken: googleTokens.access_token,
      googleRefreshToken: googleTokens.refresh_token,
      clientType: body.deviceType, // Backend expects 'clientType' instead of 'deviceType'
      deviceId: body.deviceId,
      tenantId: 1, // Add default tenantId
    };

    // Debug Backend API call
    console.log('🔍 Backend API call details:', {
      baseUrl: EXTERNAL_API_BASE_URL,
      hasApiKey: !!EXTERNAL_API_KEY,
      apiKeyPrefix: EXTERNAL_API_KEY?.substring(0, 10) + '...',
      requestPayload: {
        email: authRequest.email,
        clientType: authRequest.clientType,
        tenantId: authRequest.tenantId,
        hasGoogleId: !!authRequest.googleId,
        hasGoogleTokens: !!(authRequest.googleAccessToken && authRequest.googleRefreshToken)
      }
    });

    // Since backend uses NextAuth.js, we'll handle authentication in frontend
    // Create customer object from Google user data
    console.log('🔧 Creating frontend authentication response (NextAuth.js backend detected)');

    // Generate mock tokens for frontend compatibility
    // In production, these should be real JWT tokens from your auth service
    const authResult = {
      success: true,
      customer: {
        id: `google_${googleUser.id}`,
        email: googleUser.email,
        firstName: googleUser.given_name || '',
        lastName: googleUser.family_name || '',
        displayName: googleUser.name || '',
        image: googleUser.picture || '',
        googleId: googleUser.id,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      tokens: {
        // Use Google tokens as our auth tokens for now
        // In production, exchange these for your own JWT tokens
        accessToken: googleTokens.access_token,
        refreshToken: googleTokens.refresh_token || `refresh_${Date.now()}`,
        expiresIn: googleTokens.expires_in || 3600,
        refreshExpiresIn: 604800 // 7 days
      },
      isNewCustomer: true // Assume new customer for now
    };

    console.log('✅ Frontend authentication successful:', {
      customerId: authResult.customer.id,
      email: authResult.customer.email,
      hasTokens: !!authResult.tokens.accessToken,
      isNewCustomer: authResult.isNewCustomer
    });

    // Create customer object
    const customer: Customer = {
      id: authResult.customer.id,
      email: authResult.customer.email,
      firstName: authResult.customer.firstName,
      lastName: authResult.customer.lastName,
      displayName: authResult.customer.displayName,
      image: authResult.customer.image,
      tenantId: 1, // Default tenant
      membershipType: 'basic', // Default membership
      isEmailVerified: true, // Google emails are verified
      createdAt: authResult.customer.createdAt,
      updatedAt: authResult.customer.updatedAt,
    };

    // Create token pair
    const tokens: TokenPair = {
      accessToken: authResult.tokens.accessToken,
      refreshToken: authResult.tokens.refreshToken,
      tokenType: 'Bearer',
      expiresIn: authResult.tokens.expiresIn,
      refreshExpiresIn: authResult.tokens.refreshExpiresIn,
      expiresAt: Date.now() + (authResult.tokens.expiresIn * 1000),
    };

    const response: GoogleOAuthCallbackResponse = {
      success: true,
      customer,
      tokens,
      isNewCustomer: authResult.isNewCustomer || false,
    };

    console.log('🎉 Google OAuth callback completed successfully');

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Google OAuth callback error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'OAuth callback failed',
        errorCode: 'GOOGLE_API_ERROR'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
