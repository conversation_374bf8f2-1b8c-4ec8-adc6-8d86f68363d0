import { NextRequest, NextResponse } from 'next/server';
import { CustomerLoginRequest, CustomerLoginResponse, Customer, TokenPair } from '@/types/auth';
import { withRateLimit, rateLimitConfigs } from '@/lib/middleware/rate-limit';
import { withCSRFProtection } from '@/lib/middleware/csrf';
import { withSecurityHeaders } from '@/lib/middleware/security-headers';
import { validateAndSanitizeInput } from '@/lib/utils/sanitization';

// External API configuration
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';
const EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY || '';

// CSRF configuration
const CSRF_CONFIG = {
  secret: process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production',
};

async function handleLogin(request: NextRequest): Promise<NextResponse> {
  try {
    const body: CustomerLoginRequest = await request.json();
    
    // Validate and sanitize inputs
    const emailValidation = validateAndSanitizeInput(body.email, {
      maxLength: 255,
      sanitizationLevel: 'textOnly'
    });
    const passwordValidation = validateAndSanitizeInput(body.password, {
      maxLength: 128,
      sanitizationLevel: 'textOnly'
    });

    // Validate required fields
    if (!emailValidation.isValid || !passwordValidation.isValid || !body.tenantId || !body.deviceType) {
      return NextResponse.json(
        {
          success: false,
          error: emailValidation.error || passwordValidation.error || 'Missing required fields',
          errorCode: 'INVALID_INPUT'
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailValidation.sanitized)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format',
          errorCode: 'INVALID_INPUT'
        },
        { status: 400 }
      );
    }

    console.log('🔍 Processing customer login:', {
      email: body.email,
      tenantId: body.tenantId,
      deviceType: body.deviceType,
      deviceId: body.deviceId?.substring(0, 8) + '...',
      rememberMe: body.rememberMe
    });

    // Prepare login request for external API
    const loginRequest = {
      email: body.email,
      password: body.password,
      tenantId: body.tenantId,
      deviceType: body.deviceType,
      deviceId: body.deviceId,
      rememberMe: body.rememberMe || false,
    };

    // Call external API for customer authentication
    const externalAuthResponse = await fetch(`${EXTERNAL_API_BASE_URL}/api/auth/customer/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EXTERNAL_API_KEY}`,
      },
      body: JSON.stringify(loginRequest),
    });

    const responseText = await externalAuthResponse.text();
    
    if (!externalAuthResponse.ok) {
      console.error('External API login failed:', {
        status: externalAuthResponse.status,
        statusText: externalAuthResponse.statusText,
        response: responseText
      });

      // Parse error response if possible
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch {
        errorData = { error: 'Authentication failed' };
      }

      // Map common error codes
      let errorCode = 'INVALID_CREDENTIALS';
      if (externalAuthResponse.status === 429) {
        errorCode = 'RATE_LIMIT_EXCEEDED';
      } else if (externalAuthResponse.status === 423) {
        errorCode = 'ACCOUNT_LOCKED';
      } else if (errorData.errorCode) {
        errorCode = errorData.errorCode;
      }
      
      return NextResponse.json(
        {
          success: false,
          error: errorData.error || 'Authentication failed',
          errorCode
        },
        { status: externalAuthResponse.status }
      );
    }

    const authResult = JSON.parse(responseText);
    console.log('✅ External API login successful:', { 
      customerId: authResult.customer?.id,
      email: authResult.customer?.email 
    });

    // Create customer object
    const customer: Customer = {
      id: authResult.customer.id,
      email: authResult.customer.email,
      firstName: authResult.customer.firstName,
      lastName: authResult.customer.lastName,
      displayName: authResult.customer.displayName,
      image: authResult.customer.image,
      tenantId: authResult.customer.tenantId,
      membershipType: authResult.customer.membershipType,
      isEmailVerified: authResult.customer.isEmailVerified,
      createdAt: authResult.customer.createdAt,
      updatedAt: authResult.customer.updatedAt,
    };

    // Create token pair
    const tokens: TokenPair = {
      accessToken: authResult.tokens.accessToken,
      refreshToken: authResult.tokens.refreshToken,
      tokenType: 'Bearer',
      expiresIn: authResult.tokens.expiresIn,
      refreshExpiresIn: authResult.tokens.refreshExpiresIn,
      expiresAt: Date.now() + (authResult.tokens.expiresIn * 1000),
    };

    const response: CustomerLoginResponse = {
      success: true,
      customer,
      tokens,
    };

    console.log('🎉 Customer login completed successfully');

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Customer login error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Login failed',
        errorCode: 'INVALID_CREDENTIALS'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  return withRateLimit(
    request,
    rateLimitConfigs.auth,
    (req) => withCSRFProtection(req, CSRF_CONFIG, handleLogin)
  );
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-CSRF-Token',
    },
  });
}
