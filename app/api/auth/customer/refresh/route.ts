import { NextRequest, NextResponse } from 'next/server';
import { TokenRefreshRequest, TokenRefreshResponse, TokenPair } from '@/types/auth';

// External API configuration
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';
const EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY || '';

export async function POST(request: NextRequest) {
  try {
    const body: TokenRefreshRequest = await request.json();
    
    // Validate required fields
    if (!body.refreshToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing refresh token',
          errorCode: 'MISSING_REQUIRED_FIELD'
        },
        { status: 400 }
      );
    }

    console.log('🔍 Processing token refresh:', {
      hasRefreshToken: !!body.refreshToken,
      deviceType: body.deviceType,
      deviceId: body.deviceId?.substring(0, 8) + '...'
    });

    // Prepare refresh request for external API
    const refreshRequest = {
      refreshToken: body.refreshToken,
      deviceType: body.deviceType,
      deviceId: body.deviceId,
    };

    // Call external API for token refresh
    const externalRefreshResponse = await fetch(`${EXTERNAL_API_BASE_URL}/api/auth/customer/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EXTERNAL_API_KEY}`,
      },
      body: JSON.stringify(refreshRequest),
    });

    const responseText = await externalRefreshResponse.text();
    
    if (!externalRefreshResponse.ok) {
      console.error('External API token refresh failed:', {
        status: externalRefreshResponse.status,
        statusText: externalRefreshResponse.statusText,
        response: responseText
      });

      // Parse error response if possible
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch {
        errorData = { error: 'Token refresh failed' };
      }

      // Map common error codes
      let errorCode = 'REFRESH_TOKEN_INVALID';
      if (externalRefreshResponse.status === 429) {
        errorCode = 'RATE_LIMIT_EXCEEDED';
      } else if (externalRefreshResponse.status === 401) {
        errorCode = 'TOKEN_EXPIRED';
      } else if (errorData.errorCode) {
        errorCode = errorData.errorCode;
      }
      
      return NextResponse.json(
        {
          success: false,
          error: errorData.error || 'Token refresh failed',
          errorCode
        },
        { status: externalRefreshResponse.status }
      );
    }

    const refreshResult = JSON.parse(responseText);
    console.log('✅ External API token refresh successful');

    // Create token pair
    const tokens: TokenPair = {
      accessToken: refreshResult.tokens.accessToken,
      refreshToken: refreshResult.tokens.refreshToken,
      tokenType: 'Bearer',
      expiresIn: refreshResult.tokens.expiresIn,
      refreshExpiresIn: refreshResult.tokens.refreshExpiresIn,
      expiresAt: Date.now() + (refreshResult.tokens.expiresIn * 1000),
    };

    const response: TokenRefreshResponse = {
      success: true,
      tokens,
    };

    console.log('🎉 Token refresh completed successfully');

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Token refresh error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Token refresh failed',
        errorCode: 'REFRESH_TOKEN_INVALID'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
