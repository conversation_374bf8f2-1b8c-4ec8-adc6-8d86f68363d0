import { NextRequest, NextResponse } from 'next/server';

const API_KEY = process.env.PACKAGE_PRICING_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';
const BASE_API_URL = 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    };

    console.log('🔍 Fetching staff details for ID:', id);

    const response = await fetch(`${BASE_API_URL}/api/public/staff/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      // Return fallback instructor names
      const instructor<PERSON><PERSON><PERSON> = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON> <PERSON>',
        '<PERSON> <PERSON>', 'Tom <PERSON>', '<PERSON> <PERSON>', 'Chris Lee'
      ];
      const hash = id.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
      const instructor<PERSON>ame = instructor<PERSON>ames[hash % instructor<PERSON>ames.length];

      return NextResponse.json(
        { 
          success: false, 
          error: 'Staff not found',
          fallback: true,
          data: {
            id,
            name: instructorName,
            email: `${instructorName.toLowerCase().replace(' ', '.')}@studio.com`,
            bio: 'Experienced instructor',
            specialties: ['Pilates', 'Yoga'],
            is_active: true
          }
        },
        { status: 200, headers }
      );
    }

    const data = await response.json();
    console.log('✅ Staff data fetched successfully:', { id, name: data.data?.name });
    
    return NextResponse.json(
      {
        success: true,
        data: data.data || data,
      },
      { status: 200, headers }
    );

  } catch (error) {
    console.error('Staff API proxy error:', error);
    
    // Return fallback data
    const { id } = params;
    const instructorNames = [
      'Sarah Johnson', 'Mike Chen', 'Emma Wilson', 'David Rodriguez', 
      'Lisa Park', 'James Thompson', 'Maria Garcia', 'Alex Kim',
      'Rachel Green', 'Tom Anderson', 'Nina Patel', 'Chris Lee'
    ];
    const hash = id.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    const instructorName = instructorNames[hash % instructorNames.length];

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while fetching staff',
        fallback: true,
        data: {
          id,
          name: instructorName,
          email: `${instructorName.toLowerCase().replace(' ', '.')}@studio.com`,
          bio: 'Experienced instructor',
          specialties: ['Pilates', 'Yoga'],
          is_active: true
        }
      },
      { status: 200 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
