import { NextRequest, NextResponse } from 'next/server';

// API key stored securely in backend environment
const API_KEY = process.env.PACKAGE_PRICING_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';
const PACKAGE_PRICING_API_URL = 'http://localhost:3000/api/public/v1/package-pricing';

export async function GET(request: NextRequest) {
  try {
    // Add CORS headers for development
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    };

    // Fetch data from the external API with secure API key
    const response = await fetch(PACKAGE_PRICING_API_URL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      cache: 'no-store', // Ensure fresh data
    });

    if (!response.ok) {
      console.error('Package pricing API error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          success: false, 
          error: `Failed to fetch package pricing: ${response.statusText}`,
          status: response.status 
        },
        { status: response.status, headers }
      );
    }

    const data = await response.json();
    
    // Return the data with success wrapper
    return NextResponse.json(
      {
        success: true,
        data: data.data || data, // Handle both wrapped and unwrapped responses
        total: data.data?.length || 0,
      },
      { status: 200, headers }
    );

  } catch (error) {
    console.error('Package pricing API proxy error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while fetching package pricing',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
