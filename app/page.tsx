"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Clock, User, Star, Filter, Search, Bell, Settings } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SchedulePage } from "@/components/schedule/SchedulePage"
import { PackagePricingPage } from "@/components/package-pricing/PackagePricingPage"
import { UserProfile } from "@/components/auth/UserProfile"
import { Toaster } from "sonner"

// Mock data
const upcomingClasses = [
  {
    id: 1,
    name: "Morning Flow",
    instructor: "<PERSON>",
    time: "09:00 AM",
    date: "Today",
    type: "Group Class",
    spots: 3,
    duration: "60 min",
    level: "Intermediate",
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: 2,
    name: "1-on-1 Session",
    instructor: "<PERSON>",
    time: "02:00 PM",
    date: "Tomorrow",
    type: "Private",
    spots: 1,
    duration: "45 min",
    level: "Advanced",
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: 3,
    name: "Beginner Basics",
    instructor: "Emma Wilson",
    time: "06:00 PM",
    date: "Dec 20",
    type: "Group Class",
    spots: 5,
    duration: "50 min",
    level: "Beginner",
    image: "/placeholder.svg?height=200&width=300",
  },
]

const instructors = [
  {
    id: 1,
    name: "Sarah Johnson",
    speciality: "Reformer & Mat",
    rating: 4.9,
    experience: "8 years",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 2,
    name: "Mike Chen",
    speciality: "Athletic Pilates",
    rating: 4.8,
    experience: "6 years",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 3,
    name: "Emma Wilson",
    speciality: "Prenatal & Rehab",
    rating: 4.9,
    experience: "10 years",
    image: "/placeholder.svg?height=100&width=100",
  },
]

const recentBookings = [
  {
    id: 1,
    class: "Power Pilates",
    instructor: "Sarah Johnson",
    date: "Dec 15, 2024",
    status: "Completed",
    rating: 5,
  },
  {
    id: 2,
    class: "1-on-1 Session",
    instructor: "Mike Chen",
    date: "Dec 12, 2024",
    status: "Completed",
    rating: 5,
  },
  {
    id: 3,
    class: "Morning Flow",
    instructor: "Emma Wilson",
    date: "Dec 10, 2024",
    status: "Completed",
    rating: 4,
  },
]

export default function PilatesDashboard() {
  const [activeTab, setActiveTab] = useState("dashboard")
  const [searchQuery, setSearchQuery] = useState("")
  const [filterLevel, setFilterLevel] = useState("all")
  const [filterType, setFilterType] = useState("all")

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-stone-50 via-sage-50 to-stone-100">
      {/* Header */}
      <header className="bg-white/90 backdrop-blur-md border-b border-stone-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-sage-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
                <div>
                  <span className="text-xl font-semibold text-stone-900">Flow</span>
                  <p className="text-xs text-stone-600 -mt-1">Move Better, Live Better</p>
                </div>
              </div>
            </div>

            <nav className="hidden md:flex space-x-8">
              <button
                onClick={() => setActiveTab("dashboard")}
                className={`text-sm font-medium transition-colors py-2 ${
                  activeTab === "dashboard"
                    ? "text-sage-700 border-b-2 border-sage-600"
                    : "text-stone-600 hover:text-stone-900"
                }`}
              >
                Dashboard
              </button>
              <button
                onClick={() => setActiveTab("classes")}
                className={`text-sm font-medium transition-colors py-2 ${
                  activeTab === "classes"
                    ? "text-sage-700 border-b-2 border-sage-600"
                    : "text-stone-600 hover:text-stone-900"
                }`}
              >
                Schedules
              </button>

              <button
                onClick={() => setActiveTab("packages-pricing")}
                className={`text-sm font-medium transition-colors py-2 ${
                  activeTab === "packages-pricing"
                    ? "text-sage-700 border-b-2 border-sage-600"
                    : "text-stone-600 hover:text-stone-900"
                }`}
              >
                Package Pricing
              </button>

              <button
                onClick={() => setActiveTab("instructors")}
                className={`text-sm font-medium transition-colors py-2 ${
                  activeTab === "instructors"
                    ? "text-sage-700 border-b-2 border-sage-600"
                    : "text-stone-600 hover:text-stone-900"
                }`}
              >
                Instructors
              </button>
              <button
                onClick={() => setActiveTab("history")}
                className={`text-sm font-medium transition-colors py-2 ${
                  activeTab === "history"
                    ? "text-sage-700 border-b-2 border-sage-600"
                    : "text-stone-600 hover:text-stone-900"
                }`}
              >
                My Journey
              </button>
            </nav>

            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" className="text-stone-600 hover:text-stone-900">
                <Bell className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-stone-600 hover:text-stone-900">
                <Settings className="h-5 w-5" />
              </Button>
              <UserProfile variant="compact" />
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === "dashboard" && (
          <motion.div variants={containerVariants} initial="hidden" animate="visible" className="space-y-12">
            {/* Hero Welcome Section */}
            <motion.div variants={itemVariants} className="text-center py-12">
              <h1 className="text-5xl font-light text-stone-900 mb-6 leading-tight">
                Move Better,
                <br />
                <span className="font-medium text-sage-700">Live Better.</span>
              </h1>
              <p className="text-lg text-stone-600 max-w-2xl mx-auto mb-8 leading-relaxed">
                This is a promise not a slogan. From equipment to learning to listening, we are with you every step of
                your journey. Welcome to all that moves you.
              </p>
              <Button
                onClick={() => setActiveTab("classes")}
                className="bg-sage-600 hover:bg-sage-700 text-white px-8 py-3 text-lg font-medium rounded-full"
              >
                Explore Classes
              </Button>
            </motion.div>

            {/* Quick Stats */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-white/80 backdrop-blur-sm border-stone-200 hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-stone-600 text-sm font-medium">Classes This Month</p>
                      <p className="text-3xl font-light text-stone-900 mt-1">12</p>
                    </div>
                    <div className="w-12 h-12 bg-sage-100 rounded-full flex items-center justify-center">
                      <Calendar className="h-6 w-6 text-sage-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-stone-200 hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-stone-600 text-sm font-medium">Hours Practiced</p>
                      <p className="text-3xl font-light text-stone-900 mt-1">18</p>
                    </div>
                    <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center">
                      <Clock className="h-6 w-6 text-emerald-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-stone-200 hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-stone-600 text-sm font-medium">Favorite Instructor</p>
                      <p className="text-xl font-medium text-stone-900 mt-1">Sarah J.</p>
                    </div>
                    <div className="w-12 h-12 bg-rose-100 rounded-full flex items-center justify-center">
                      <User className="h-6 w-6 text-rose-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-stone-200 hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-stone-600 text-sm font-medium">Average Rating</p>
                      <p className="text-3xl font-light text-stone-900 mt-1">4.9</p>
                    </div>
                    <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
                      <Star className="h-6 w-6 text-amber-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Upcoming Classes */}
            <motion.div variants={itemVariants}>
              <div className="flex justify-between items-center mb-8">
                <div>
                  <h2 className="text-3xl font-light text-stone-900 mb-2">Your Upcoming Sessions</h2>
                  <p className="text-stone-600">Stay consistent with your practice</p>
                </div>
                <Button
                  onClick={() => setActiveTab("classes")}
                  variant="outline"
                  className="border-sage-200 text-sage-700 hover:bg-sage-50"
                >
                  View All Classes
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {upcomingClasses.map((classItem, index) => (
                  <motion.div
                    key={classItem.id}
                    variants={itemVariants}
                    whileHover={{ y: -8 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm border-stone-200">
                      <div className="aspect-[4/3] relative">
                        <img
                          src={classItem.image || "/placeholder.svg?height=240&width=320&query=pilates class"}
                          alt={classItem.name}
                          className="w-full h-full object-cover"
                        />
                        <Badge className="absolute top-4 left-4 bg-white/95 text-stone-700 border-0 font-medium">
                          {classItem.type}
                        </Badge>
                      </div>
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <h3 className="text-xl font-medium text-stone-900">{classItem.name}</h3>
                          <Badge
                            variant="outline"
                            className={`border-0 font-medium ${
                              classItem.level === "Beginner"
                                ? "bg-emerald-100 text-emerald-700"
                                : classItem.level === "Intermediate"
                                  ? "bg-amber-100 text-amber-700"
                                  : "bg-rose-100 text-rose-700"
                            }`}
                          >
                            {classItem.level}
                          </Badge>
                        </div>

                        <p className="text-stone-600 mb-4 font-medium">with {classItem.instructor}</p>

                        <div className="flex justify-between items-center text-sm text-stone-500 mb-6">
                          <span className="font-medium">
                            {classItem.date} • {classItem.time}
                          </span>
                          <span>{classItem.duration}</span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm text-stone-600 font-medium">{classItem.spots} spots left</span>
                          <Button size="sm" className="bg-sage-600 hover:bg-sage-700 text-white rounded-full px-6">
                            {classItem.type === "Private" ? "Reschedule" : "Join Class"}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Recent Activity */}
            <motion.div variants={itemVariants}>
              <div className="mb-8">
                <h2 className="text-3xl font-light text-stone-900 mb-2">Your Journey</h2>
                <p className="text-stone-600">Track your progress and celebrate your achievements</p>
              </div>
              <Card className="bg-white/90 backdrop-blur-sm border-stone-200">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    {recentBookings.map((booking) => (
                      <div
                        key={booking.id}
                        className="flex items-center justify-between py-4 border-b border-stone-100 last:border-b-0"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-3 h-3 bg-sage-500 rounded-full"></div>
                          <div>
                            <p className="font-medium text-stone-900 text-lg">{booking.class}</p>
                            <p className="text-stone-600">
                              with {booking.instructor} • {booking.date}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            {[...Array(booking.rating)].map((_, i) => (
                              <Star key={i} className="h-4 w-4 fill-amber-400 text-amber-400" />
                            ))}
                          </div>
                          <Badge variant="outline" className="bg-sage-50 text-sage-700 border-sage-200 font-medium">
                            {booking.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}

        {/* Replace the classes tab with SchedulePage component */}
        {activeTab === "classes" && (
          <SchedulePage tenantId={1} className="animate-in fade-in-0 duration-500" />
        )}

        {/* Package Pricing tab */}
        {activeTab === "packages-pricing" && (
          <div className="animate-in fade-in-0 duration-500">
            <PackagePricingPage />
          </div>
        )}

        {activeTab === "instructors" && (
          <motion.div variants={containerVariants} initial="hidden" animate="visible" className="space-y-8">
            <motion.div variants={itemVariants}>
              <h1 className="text-3xl font-bold text-slate-900 mb-6">Our Instructors</h1>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {instructors.map((instructor) => (
                  <motion.div
                    key={instructor.id}
                    variants={itemVariants}
                    whileHover={{ y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Card className="text-center hover:shadow-lg transition-shadow">
                      <CardContent className="p-6">
                        <Avatar className="w-24 h-24 mx-auto mb-4">
                          <AvatarImage src={instructor.image || "/placeholder.svg"} />
                          <AvatarFallback>
                            {instructor.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>

                        <h3 className="text-xl font-semibold text-slate-900 mb-2">{instructor.name}</h3>
                        <p className="text-slate-600 mb-3">{instructor.speciality}</p>

                        <div className="flex items-center justify-center space-x-4 mb-4">
                          <div className="flex items-center">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                            <span className="text-sm font-medium">{instructor.rating}</span>
                          </div>
                          <div className="text-sm text-slate-600">{instructor.experience} experience</div>
                        </div>

                        <Button className="w-full">Book Session</Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}

        {activeTab === "history" && (
          <motion.div variants={containerVariants} initial="hidden" animate="visible" className="space-y-8">
            <motion.div variants={itemVariants}>
              <h1 className="text-3xl font-bold text-slate-900 mb-6">My Booking History</h1>

              <Card>
                <CardContent className="p-6">
                  <div className="space-y-6">
                    {recentBookings.map((booking) => (
                      <div
                        key={booking.id}
                        className="flex items-center justify-between p-4 border border-slate-200 rounded-lg"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Calendar className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-slate-900">{booking.class}</h3>
                            <p className="text-slate-600">with {booking.instructor}</p>
                            <p className="text-sm text-slate-500">{booking.date}</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            {[...Array(booking.rating)].map((_, i) => (
                              <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            ))}
                          </div>
                          <Badge variant="secondary">{booking.status}</Badge>
                          <Button variant="outline" size="sm">
                            Book Again
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </main>
      
      {/* Toast notifications for SchedulePage */}
      <Toaster position="top-right" />
    </div>
  )
}