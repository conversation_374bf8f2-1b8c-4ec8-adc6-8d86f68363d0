import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { AuthProvider } from "@/lib/contexts/AuthContext"
import { Toaster } from "sonner"
import { QueryProvider } from "@/hooks/providers/QueryProvider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Flow - Premium Pilates Booking Platform",
  description:
    "Book your perfect pilates session with certified instructors. Group classes and private sessions available.",
  keywords: "pilates, booking, fitness, wellness, private sessions, group classes",
  authors: [{ name: "Flow Team" }],
  openGraph: {
    title: "PilatesFlow - Premium Pilates Booking Platform",
    description: "Book your perfect pilates session with certified instructors",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "PilatesFlow - Premium Pilates Booking Platform",
    description: "Book your perfect pilates session with certified instructors",
  },
  robots: {
    index: true,
    follow: true,
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <QueryProvider>
          <AuthProvider>
            {children}
            <Toaster />
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  )
}
