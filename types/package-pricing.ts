export interface PackagePricing {
  id: string;
  packageId: string;
  packageName: string;
  packageDescription: string;
  pricingGroupId: string;
  pricingGroupName: string;
  price: number;
  creditAmount: number;
  currency: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PackagePricingResponse {
  success: boolean;
  data: PackagePricing[];
  total: number;
  error?: string;
}

export interface PackagePricingFilters {
  isActive?: boolean;
  currency?: string;
  minPrice?: number;
  maxPrice?: number;
  search?: string;
}

export interface PackagePricingListState {
  packages: PackagePricing[];
  loading: boolean;
  error: string | null;
  total: number;
  filters: PackagePricingFilters;
}

// Utility types for package pricing display
export interface PackagePricingCard {
  id: string;
  name: string;
  description: string;
  price: number;
  creditAmount: number;
  currency: string;
  isRecommended?: boolean;
  features: string[];
  buttonText: string;
  buttonVariant: 'default' | 'outline' | 'secondary';
}

// Pricing display options
export interface PricingDisplayOptions {
  showCurrency: boolean;
  showCredits: boolean;
  showDescription: boolean;
  compactMode: boolean;
}
