export interface ClassSchedule {
  id: string;
  class_id: string;
  tenant_id: number;
  location_id: string | null;
  facility_id: string | null;
  staff_id: string | null;
  start_date: string | null;
  end_date: string | null;
  start_time: string;
  end_time: string;
  duration: number;
  calender_color: string;
  repeat_rule: 'none' | 'daily' | 'weekly' | 'monthly';
  pax: number;
  waitlist: number;
  allow_classpass: boolean;
  is_private: boolean;
  publish_now: boolean;
  publish_at: string | null;
  auto_cancel_if_minimum_not_met: boolean;
  booking_window_start: string | null;
  booking_window_end: string | null;
  check_in_window_start: string | null;
  check_in_window_end: string | null;
  late_cancellation_rule: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface ScheduleFilters {
  tenantId: number;
  search?: string;
  startDate?: string;
  endDate?: string;
  locationId?: string;
  facilityId?: string;
  staffId?: string;
  isPrivate?: boolean;
  allowClasspass?: boolean;
  limit?: number;
  offset?: number;
}

export interface ScheduleListState {
  schedules: ClassSchedule[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  total: number;
  filters: ScheduleFilters;
}
