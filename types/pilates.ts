export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  membershipType: "basic" | "premium" | "unlimited"
  joinDate: Date
  totalClasses: number
  favoriteInstructor?: string
}

export interface Instructor {
  id: string
  name: string
  bio: string
  specialties: string[]
  experience: string
  rating: number
  totalReviews: number
  avatar: string
  certifications: string[]
  availability: TimeSlot[]
}

export interface PilatesClass {
  id: string
  name: string
  description: string
  instructor: Instructor
  type: "group" | "private" | "semi-private"
  level: "beginner" | "intermediate" | "advanced"
  duration: number // in minutes
  maxCapacity: number
  currentBookings: number
  price: number
  equipment: string[]
  schedule: ClassSchedule[]
  image: string
  tags: string[]
}

export interface ClassSchedule {
  id: string
  classId: string
  date: Date
  startTime: string
  endTime: string
  availableSpots: number
  status: "available" | "full" | "cancelled"
}

export interface Booking {
  id: string
  userId: string
  classId: string
  scheduleId: string
  bookingDate: Date
  status: "confirmed" | "cancelled" | "completed" | "no-show"
  paymentStatus: "pending" | "paid" | "refunded"
  notes?: string
  rating?: number
  review?: string
}

export interface TimeSlot {
  day: "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday"
  startTime: string
  endTime: string
}

export interface PaymentMethod {
  id: string
  type: "card" | "bank" | "digital_wallet"
  last4?: string
  brand?: string
  isDefault: boolean
}

export interface Membership {
  id: string
  userId: string
  type: "basic" | "premium" | "unlimited"
  startDate: Date
  endDate: Date
  remainingClasses?: number
  autoRenew: boolean
  price: number
}

export interface Review {
  id: string
  userId: string
  instructorId: string
  classId: string
  rating: number
  comment: string
  date: Date
  helpful: number
}

export interface Notification {
  id: string
  userId: string
  type: "booking_confirmation" | "class_reminder" | "cancellation" | "promotion"
  title: string
  message: string
  read: boolean
  date: Date
  actionUrl?: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form Types
export interface BookingFormData {
  classId: string
  scheduleId: string
  notes?: string
  paymentMethodId: string
}

export interface ProfileFormData {
  name: string
  email: string
  phone?: string
  emergencyContact?: string
  medicalConditions?: string
  fitnessGoals?: string[]
}

export interface ReviewFormData {
  rating: number
  comment: string
  instructorId: string
  classId: string
}

// Filter Types
export interface ClassFilters {
  type?: "group" | "private" | "semi-private"
  level?: "beginner" | "intermediate" | "advanced"
  instructor?: string
  date?: Date
  timeRange?: {
    start: string
    end: string
  }
  priceRange?: {
    min: number
    max: number
  }
  equipment?: string[]
  tags?: string[]
}

export interface InstructorFilters {
  specialties?: string[]
  rating?: number
  experience?: string
  availability?: TimeSlot[]
}

// Dashboard Stats
export interface DashboardStats {
  totalClasses: number
  hoursThisMonth: number
  favoriteInstructor: string
  averageRating: number
  upcomingClasses: number
  completedClasses: number
  cancelledClasses: number
  totalSpent: number
}

// Booking Calendar
export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end: Date
  type: "class" | "private_session"
  instructor: string
  status: "confirmed" | "pending" | "cancelled"
  color: string
}
