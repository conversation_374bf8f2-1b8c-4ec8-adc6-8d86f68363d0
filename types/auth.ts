// Customer Authentication Types
export interface Customer {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  image?: string;
  tenantId: number;
  membershipType?: string;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

// Token Management
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  tokenType: 'Bearer';
  expiresIn: number; // seconds
  refreshExpiresIn?: number; // seconds
  expiresAt: number; // timestamp
}

// Authentication State
export interface AuthState {
  isAuthenticated: boolean;
  customer: Customer | null;
  tokens: TokenPair | null;
  isLoading: boolean;
}

// Device Context
export interface DeviceContext {
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  userAgent?: string;
  ipAddress?: string;
}

// OAuth Flow Types
export interface GoogleOAuthInitRequest {
  tenantId: number;
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  redirectUri?: string;
  codeChallenge?: string; // For mobile PKCE
  codeChallengeMethod?: 'S256';
}

export interface GoogleOAuthInitResponse {
  success: boolean;
  authorizationUrl: string;
  state: string;
  codeChallenge?: string;
}

export interface GoogleOAuthCallbackRequest {
  code: string;
  state: string;
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  codeVerifier?: string; // For mobile PKCE
}

export interface GoogleOAuthCallbackResponse {
  success: boolean;
  customer: Customer;
  tokens: TokenPair;
  isNewCustomer: boolean;
}

// Credentials Authentication
export interface CustomerLoginRequest {
  email: string;
  password: string;
  tenantId: number;
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  rememberMe?: boolean;
}

export interface CustomerLoginResponse {
  success: boolean;
  customer: Customer;
  tokens: TokenPair;
}

// Token Refresh
export interface TokenRefreshRequest {
  refreshToken: string;
  deviceType?: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
}

export interface TokenRefreshResponse {
  success: boolean;
  tokens: TokenPair;
}

// Session Management
export interface SessionContext {
  customerId: string;
  tenantId: number;
  ipAddress?: string;
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  userAgent?: string;
}

export interface CustomerSession {
  id: string;
  customerId: string;
  tenantId: number;
  jti: string; // JWT ID
  tokenHash: string;
  refreshTokenHash?: string;
  deviceType?: string;
  deviceId?: string;
  userAgent?: string;
  ipAddress?: string;
  oauthProvider?: string;
  oauthAccountId?: string;
  issuedAt: string;
  expiresAt: string;
  refreshExpiresAt?: string;
  lastUsedAt: string;
  revokedAt?: string;
  revokedReason?: string;
  isActive: boolean;
  isSuspicious: boolean;
}

// JWT Payload
export interface CustomerJWTPayload {
  sub: string; // customer ID
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  tenantId: number;
  membershipType?: string;
  sessionId: string;
  deviceType?: string;
  iat: number;
  exp: number;
  jti: string;
}

// Error Handling
export interface AuthError {
  success: false;
  error: string;
  errorCode: string;
  details?: any;
}

// Common error codes
export const AUTH_ERROR_CODES = {
  // Authentication Errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  TENANT_MISMATCH: 'TENANT_MISMATCH',

  // OAuth Errors
  OAUTH_STATE_MISMATCH: 'OAUTH_STATE_MISMATCH',
  OAUTH_CODE_EXPIRED: 'OAUTH_CODE_EXPIRED',
  GOOGLE_API_ERROR: 'GOOGLE_API_ERROR',

  // Token Errors
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  REFRESH_TOKEN_INVALID: 'REFRESH_TOKEN_INVALID',
  SESSION_REVOKED: 'SESSION_REVOKED',

  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',

  // Validation Errors
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
} as const;

export type AuthErrorCode = keyof typeof AUTH_ERROR_CODES;

// Auth Context Type
export interface AuthContextType {
  isAuthenticated: boolean;
  customer: Customer | null;
  tokens: TokenPair | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<boolean>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
}

// Booking Context (for protected flow)
export interface BookingContext {
  scheduleId: string;
  returnUrl?: string;
  bookingData?: any;
}

// Rate Limiting
export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
}

// Security Context
export interface SecurityContext {
  ipAddress: string;
  userAgent: string;
  riskScore?: number;
  isVPN?: boolean;
  isTor?: boolean;
  countryCode?: string;
}

// Validation Result
export interface ValidationResult {
  valid: boolean;
  payload?: CustomerJWTPayload;
  errorCode?: AuthErrorCode;
  error?: string;
}

// PKCE Challenge (for mobile)
export interface PKCEChallenge {
  codeVerifier: string;
  codeChallenge: string;
  codeChallengeMethod: 'S256';
}

// Auth Event Logging
export interface AuthEvent {
  customerId?: string;
  tenantId: number;
  event: 'login_success' | 'login_failed' | 'logout' | 'token_refresh' | 'session_revoked';
  method: 'google_oauth' | 'credentials' | 'refresh_token';
  status: 'success' | 'failed' | 'blocked';
  ipAddress: string;
  userAgent?: string;
  errorCode?: string;
  riskScore?: number;
}

// Storage Interface
export interface TokenStorage {
  getTokens(): Promise<TokenPair | null>;
  setTokens(tokens: TokenPair): Promise<void>;
  clearTokens(): Promise<void>;
  getCSRFToken(): Promise<string | null>;
  setCSRFToken(token: string): Promise<void>;
}
