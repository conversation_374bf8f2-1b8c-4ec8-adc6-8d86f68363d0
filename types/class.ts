export interface Class {
  id: string;
  name: string;
  description: string | null;
  tenant_id: number;
  duration: number;
  max_capacity: number;
  price: number;
  is_active: boolean;
  class_type: 'group' | 'private' | 'semi-private';
  level: 'beginner' | 'intermediate' | 'advanced' | null;
  equipment_required: string[] | null;
  tags: string[] | null;
  image_url: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Location {
  id: string;
  name: string;
  description: string | null;
  tenant_id: number;
  address: string | null;
  is_active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Facility {
  id: string;
  name: string;
  description: string | null;
  location_id: string;
  tenant_id: number;
  capacity: number;
  equipment: string[] | null;
  is_active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Staff {
  id: string;
  name: string;
  email: string;
  bio: string | null;
  specialties: string[] | null;
  certifications: string[] | null;
  experience_years: number | null;
  rating: number | null;
  avatar_url: string | null;
  is_active: boolean;
  tenant_id: number;
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface ClassResponse {
  success: boolean;
  data: Class;
  error?: string;
}

export interface LocationResponse {
  success: boolean;
  data: Location;
  error?: string;
}

export interface FacilityResponse {
  success: boolean;
  data: Facility;
  error?: string;
}

export interface StaffResponse {
  success: boolean;
  data: Staff;
  error?: string;
}

// Filters
export interface ClassFilters {
  tenantId: number;
  search?: string;
  classType?: 'group' | 'private' | 'semi-private';
  level?: 'beginner' | 'intermediate' | 'advanced';
  isActive?: boolean;
  limit?: number;
  offset?: number;
}

export interface LocationFilters {
  tenantId: number;
  search?: string;
  isActive?: boolean;
  limit?: number;
  offset?: number;
}

export interface FacilityFilters {
  tenantId: number;
  locationId?: string;
  search?: string;
  isActive?: boolean;
  limit?: number;
  offset?: number;
}

export interface StaffFilters {
  tenantId: number;
  search?: string;
  specialties?: string[];
  isActive?: boolean;
  limit?: number;
  offset?: number;
}
