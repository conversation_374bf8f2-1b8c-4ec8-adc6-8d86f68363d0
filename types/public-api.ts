// 📚 Types untuk Public Classes API Integration
// Sesuai dengan dokumentasi public-classes-frontend-integration.md

export interface PublicClassDTO {
  id: string;
  name: string;
  description: string;
  category: string;
  startDate: string; // ISO string
  endDate: string;   // ISO string
  isActive: boolean;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

export interface PublicClassesResponse {
  success: boolean;
  data: {
    classes: PublicClassDTO[];
  };
  meta: {
    total: number;
    page: number;
    limit: number;
    pageCount: number;
  };
}

export interface PublicClassResponse {
  success: boolean;
  data: PublicClassDTO;
}

export interface PublicApiError {
  success: false;
  error: string;
}

// Query parameters untuk filtering dan pagination
export interface PublicClassesParams {
  tenantId?: string;
  limit?: number;
  page?: number;
  id?: string;
}

// Enhanced error types untuk better error handling
export interface PublicClassesError {
  message: string;
  status?: number;
  code?: string;
}

// API Configuration types
export interface PublicApiConfig {
  apiKey: string;
  baseUrl: string;
}

// Pagination metadata
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  pageCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Enhanced response dengan pagination info
export interface PublicClassesResponseEnhanced {
  success: boolean;
  data: PublicClassDTO[];
  meta: PaginationMeta;
}

// Filter options untuk advanced filtering
export interface PublicClassesFilters {
  tenantId?: string;
  category?: string;
  isActive?: boolean;
  startDateFrom?: string;
  startDateTo?: string;
  search?: string;
}

// Sort options
export interface PublicClassesSortOptions {
  field: 'name' | 'startDate' | 'endDate' | 'category' | 'createdAt';
  direction: 'asc' | 'desc';
}

// Complete query options
export interface PublicClassesQueryOptions {
  filters?: PublicClassesFilters;
  sort?: PublicClassesSortOptions;
  pagination?: {
    page: number;
    limit: number;
  };
}

// API Response wrapper untuk consistency
export type PublicApiResponse<T> = {
  success: true;
  data: T;
  meta?: any;
} | {
  success: false;
  error: string;
};

// Hook return types untuk better TypeScript support
export interface UsePublicClassesReturn {
  data: PublicClassDTO[] | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isSuccess: boolean;
  refetch: () => void;
  hasData: boolean;
  errorMessage: string | null;
  isUnauthorized: boolean;
  isNotFound: boolean;
  isNetworkError: boolean;
  retry: () => void;
}

export interface UsePublicClassReturn {
  data: PublicClassDTO | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isSuccess: boolean;
  refetch: () => void;
}

// Component props types
export interface ClassesListProps {
  tenantId?: string;
  limit?: number;
  showPagination?: boolean;
  showFilters?: boolean;
  onClassSelect?: (classItem: PublicClassDTO) => void;
}

export interface ClassCardProps {
  classItem: PublicClassDTO;
  onClick?: (classItem: PublicClassDTO) => void;
  showActions?: boolean;
}

// Loading and error state types
export interface LoadingState {
  isLoading: boolean;
  loadingMessage?: string;
}

export interface ErrorState {
  isError: boolean;
  errorMessage: string;
  errorType: 'network' | 'unauthorized' | 'not-found' | 'server' | 'unknown';
  canRetry: boolean;
}

// Cache configuration
export interface CacheConfig {
  staleTime: number;
  gcTime: number;
  refetchOnWindowFocus: boolean;
  refetchOnReconnect: boolean;
  retry: boolean | number | ((failureCount: number, error: any) => boolean);
}
