// 🧪 Unit Tests untuk Public Classes API
// Testing sesuai dengan dokumentasi public-classes-frontend-integration.md

import { publicClassesApi, PublicClassesApiError } from '@/lib/api/public-classes-client';
import { PublicClassDTO } from '@/types/public-api';

// Mock fetch untuk testing
global.fetch = jest.fn();

// Mock environment variables
const originalEnv = process.env;

beforeEach(() => {
  jest.resetAllMocks();
  process.env = {
    ...originalEnv,
    NEXT_PUBLIC_API_URL: 'http://localhost:3000',
    NEXT_PUBLIC_CLASSES_API_KEY: 'pk_test_key_123',
  };
});

afterEach(() => {
  process.env = originalEnv;
});

// Mock data yang bisa diakses global
const mockClasses: PublicClassDTO[] = [
  {
    id: 'cls_123',
    name: 'Test Yoga Class',
    description: 'A test yoga class for beginners',
    category: 'Fitness',
    startDate: '2024-01-15T09:00:00.000Z',
    endDate: '2024-01-15T10:30:00.000Z',
    isActive: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
];

describe('Public Classes API Client', () => {

  describe('getClasses', () => {
    it('should fetch classes successfully', async () => {
      const mockResponse = {
        success: true,
        data: { classes: mockClasses },
        meta: { total: 1, page: 1, limit: 20, pageCount: 1 },
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await publicClassesApi.getClasses({ tenantId: '1' });

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/public/v1/classes?tenantId=1',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'x-api-key': 'pk_test_key_123',
          }),
        })
      );

      expect(result).toEqual(mockClasses);
    });

    it('should handle API errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
      });

      await expect(publicClassesApi.getClasses()).rejects.toThrow(
        PublicClassesApiError
      );
    });

    it('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(
        new TypeError('Failed to fetch')
      );

      await expect(publicClassesApi.getClasses()).rejects.toThrow(
        'Koneksi bermasalah. Periksa internet Anda.'
      );
    });

    it('should handle rate limiting', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 429,
        headers: {
          get: (name: string) => (name === 'Retry-After' ? '60' : null),
        },
      });

      await expect(publicClassesApi.getClasses()).rejects.toThrow(
        'Rate limit exceeded'
      );
    });
  });

  describe('getClassById', () => {
    it('should fetch single class successfully', async () => {
      const mockResponse = {
        success: true,
        data: mockClasses[0],
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await publicClassesApi.getClassById('cls_123');

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/public/v1/classes?id=cls_123',
        expect.objectContaining({
          headers: expect.objectContaining({
            'x-api-key': 'pk_test_key_123',
          }),
        })
      );

      expect(result).toEqual(mockClasses[0]);
    });

    it('should handle class not found', async () => {
      const mockResponse = {
        success: false,
        error: 'Class not found',
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await expect(publicClassesApi.getClassById('invalid_id')).rejects.toThrow(
        'Class not found'
      );
    });
  });

  describe('healthCheck', () => {
    it('should return true when API is healthy', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: { classes: [] } }),
      });

      const result = await publicClassesApi.healthCheck();
      expect(result).toBe(true);
    });

    it('should return false when API is unhealthy', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await publicClassesApi.healthCheck();
      expect(result).toBe(false);
    });
  });
});

describe('PublicClassesApiError', () => {
  it('should determine retryability correctly', () => {
    // Client errors (4xx) should not be retryable except 408, 429
    const unauthorizedError = new PublicClassesApiError('Unauthorized', 401);
    expect(unauthorizedError.isRetryable).toBe(false);

    const timeoutError = new PublicClassesApiError('Timeout', 408);
    expect(timeoutError.isRetryable).toBe(true);

    const rateLimitError = new PublicClassesApiError('Rate Limited', 429);
    expect(rateLimitError.isRetryable).toBe(true);

    // Server errors (5xx) should be retryable
    const serverError = new PublicClassesApiError('Server Error', 500);
    expect(serverError.isRetryable).toBe(true);

    // Network errors should be retryable
    const networkError = new PublicClassesApiError('Network Error', 0);
    expect(networkError.isRetryable).toBe(true);
  });
});

// Integration test untuk API route
describe('API Route Integration', () => {
  it('should validate API key correctly', async () => {
    // Mock the API route behavior
    const mockRequest = {
      headers: {
        get: (name: string) => {
          if (name === 'x-api-key') return 'pk_test_key_123';
          return null;
        },
      },
      url: 'http://localhost:3000/api/public/v1/classes?tenantId=1',
    };

    // This would be tested with actual API route in integration tests
    expect(mockRequest.headers.get('x-api-key')).toBe('pk_test_key_123');
  });
});

// Performance tests
describe('Performance Tests', () => {
  it('should handle large datasets efficiently', async () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      ...mockClasses[0],
      id: `cls_${i}`,
      name: `Class ${i}`,
    }));

    const mockResponse = {
      success: true,
      data: { classes: largeDataset },
      meta: { total: 1000, page: 1, limit: 1000, pageCount: 1 },
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const startTime = Date.now();
    const result = await publicClassesApi.getClasses({ limit: 1000 });
    const endTime = Date.now();

    expect(result).toHaveLength(1000);
    expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
  });
});

// Error boundary tests
describe('Error Handling', () => {
  it('should handle malformed JSON responses', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => {
        throw new SyntaxError('Unexpected token');
      },
    });

    await expect(publicClassesApi.getClasses()).rejects.toThrow();
  });

  it('should handle empty responses', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({}),
    });

    await expect(publicClassesApi.getClasses()).rejects.toThrow();
  });
});

// Cache and optimization tests
describe('Caching Strategy', () => {
  it('should use appropriate cache headers', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { classes: mockClasses },
      }),
    });

    await publicClassesApi.getClasses();

    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        headers: expect.objectContaining({
          'Cache-Control': 'no-cache',
        }),
      })
    );
  });
});
