import { renderHook, act, waitFor } from '@testing-library/react';
import { useSchedules } from '@/lib/hooks/useSchedules';
import { scheduleApi } from '@/lib/api/endpoints';

// Mock the API
jest.mock('@/lib/api/endpoints');
const mockScheduleApi = scheduleApi as jest.Mocked<typeof scheduleApi>;

const mockSchedules = [
  {
    id: 'test-1',
    class_id: 'class-1',
    tenant_id: 1,
    start_time: '2024-01-01T09:00:00Z',
    end_time: '2024-01-01T10:00:00Z',
    duration: 60,
    pax: 10,
    waitlist: 2,
    is_private: false,
    allow_classpass: true,
    calender_color: '#3b82f6',
    repeat_rule: 'none' as const,
    location_id: null,
    facility_id: null,
    staff_id: null,
    start_date: null,
    end_date: null,
    publish_now: true,
    publish_at: null,
    auto_cancel_if_minimum_not_met: false,
    booking_window_start: null,
    booking_window_end: null,
    check_in_window_start: null,
    check_in_window_end: null,
    late_cancellation_rule: null,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

describe('useSchedules', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch schedules on mount', async () => {
    mockScheduleApi.getSchedules.mockResolvedValue({
      data: mockSchedules,
      total: 1,
      hasMore: false,
      limit: 10,
      offset: 0,
    });

    const { result } = renderHook(() => useSchedules());

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.schedules).toEqual(mockSchedules);
    expect(result.current.total).toBe(1);
    expect(result.current.hasMore).toBe(false);
  });

  it('should handle API errors', async () => {
    const errorMessage = 'Failed to fetch';
    mockScheduleApi.getSchedules.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useSchedules());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.schedules).toEqual([]);
  });

  it('should update filters', async () => {
    mockScheduleApi.getSchedules.mockResolvedValue({
      data: [],
      total: 0,
      hasMore: false,
      limit: 10,
      offset: 0,
    });

    const { result } = renderHook(() => useSchedules());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    act(() => {
      result.current.updateFilters({ search: 'yoga' });
    });

    expect(result.current.filters.search).toBe('yoga');
    expect(result.current.filters.offset).toBe(0); // Should reset pagination
  });

  it('should load more schedules', async () => {
    const firstBatch = [mockSchedules[0]];
    const secondBatch = [{ ...mockSchedules[0], id: 'test-2' }];

    mockScheduleApi.getSchedules
      .mockResolvedValueOnce({
        data: firstBatch,
        total: 2,
        hasMore: true,
        limit: 1,
        offset: 0,
      })
      .mockResolvedValueOnce({
        data: secondBatch,
        total: 2,
        hasMore: false,
        limit: 1,
        offset: 1,
      });

    const { result } = renderHook(() => useSchedules({ initialFilters: { limit: 1 } }));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.schedules).toHaveLength(1);
    expect(result.current.hasMore).toBe(true);

    await act(async () => {
      await result.current.loadMore();
    });

    expect(result.current.schedules).toHaveLength(2);
    expect(result.current.hasMore).toBe(false);
  });
});