// 🔄 TanStack Query Provider Setup
// Provider untuk React Query dengan konfigurasi optimal

'use client';

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time - data dianggap fresh selama 5 menit
      staleTime: 5 * 60 * 1000,
      // Cache time - data disimpan di cache selama 10 menit
      gcTime: 10 * 60 * 1000,
      // Retry configuration
      retry: (failureCount, error: any) => {
        // Jangan retry untuk 4xx errors kecuali 408, 429
        if (error?.status >= 400 && error?.status < 500) {
          if (error?.status === 408 || error?.status === 429) {
            return failureCount < 2;
          }
          return false;
        }
        // Retry untuk network errors dan 5xx errors
        return failureCount < 3;
      },
      // Retry delay dengan exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Refetch on window focus (hanya di production)
      refetchOnWindowFocus: process.env.NODE_ENV === 'production',
      // Refetch on reconnect
      refetchOnReconnect: true,
      // Background refetch interval (5 menit)
      refetchInterval: 5 * 60 * 1000,
    },
    mutations: {
      // Retry mutations yang gagal
      retry: 1,
      retryDelay: 1000,
    },
  },
});

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* React Query Devtools - hanya di development */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false}
          position="bottom-right"
          buttonPosition="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
}

// Export query client untuk digunakan di tempat lain jika diperlukan
export { queryClient };
