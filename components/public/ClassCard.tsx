// 🎴 Class Card Component
// Komponen card individual untuk menampilkan detail class

'use client';

import React from 'react';
import { PublicClassDTO, ClassCardProps } from '@/types/public-api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar,
  Clock,
  Tag,
  Users,
  MapPin,
  Star
} from 'lucide-react';

export const ClassCard: React.FC<ClassCardProps> = ({ 
  classItem, 
  onClick, 
  showActions = false 
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.round(diffMs / (1000 * 60));
    
    if (diffMins < 60) {
      return `${diffMins} menit`;
    } else {
      const hours = Math.floor(diffMins / 60);
      const mins = diffMins % 60;
      return mins > 0 ? `${hours}j ${mins}m` : `${hours} jam`;
    }
  };

  const isUpcoming = (startDate: string) => {
    return new Date(startDate) > new Date();
  };

  const isPast = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  const getStatusBadge = () => {
    if (!classItem.isActive) {
      return (
        <Badge variant="secondary" className="bg-gray-100 text-gray-600">
          Tidak Aktif
        </Badge>
      );
    }

    if (isPast(classItem.endDate)) {
      return (
        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          Selesai
        </Badge>
      );
    }

    if (isUpcoming(classItem.startDate)) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800">
          Akan Datang
        </Badge>
      );
    }

    return (
      <Badge variant="default" className="bg-orange-100 text-orange-800">
        Berlangsung
      </Badge>
    );
  };

  return (
    <Card 
      className={`
        hover:shadow-lg transition-all duration-200 
        ${onClick ? 'cursor-pointer hover:scale-[1.02]' : ''} 
        ${!classItem.isActive ? 'opacity-75' : ''}
      `}
      onClick={() => onClick?.(classItem)}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start gap-3">
          <CardTitle className="text-lg line-clamp-2 flex-1">
            {classItem.name}
          </CardTitle>
          {getStatusBadge()}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Description */}
        <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
          {classItem.description}
        </p>
        
        {/* Class Details */}
        <div className="space-y-3 text-sm">
          {/* Category */}
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-blue-500" />
            <span className="font-medium text-gray-700">Kategori:</span>
            <Badge variant="outline" className="text-xs">
              {classItem.category}
            </Badge>
          </div>
          
          {/* Date */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-green-500" />
            <span className="font-medium text-gray-700">Tanggal:</span>
            <span className="text-gray-600">
              {formatDate(classItem.startDate)}
            </span>
          </div>
          
          {/* Time & Duration */}
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-orange-500" />
            <span className="font-medium text-gray-700">Waktu:</span>
            <span className="text-gray-600">
              {formatTime(classItem.startDate)} - {formatTime(classItem.endDate)}
            </span>
            <Badge variant="secondary" className="text-xs ml-auto">
              {getDuration(classItem.startDate, classItem.endDate)}
            </Badge>
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="pt-4 border-t border-gray-100">
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="flex-1"
                onClick={(e) => {
                  e.stopPropagation();
                  onClick?.(classItem);
                }}
              >
                <Users className="h-4 w-4 mr-2" />
                Lihat Detail
              </Button>
              
              {classItem.isActive && isUpcoming(classItem.startDate) && (
                <Button 
                  size="sm" 
                  className="flex-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle booking action
                    console.log('Book class:', classItem.id);
                  }}
                >
                  <Star className="h-4 w-4 mr-2" />
                  Book Now
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Footer info */}
        <div className="pt-2 border-t border-gray-50">
          <div className="flex justify-between items-center text-xs text-gray-400">
            <span>ID: {classItem.id}</span>
            <span>
              Dibuat: {new Date(classItem.createdAt).toLocaleDateString('id-ID')}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Compact version untuk list views
export const ClassCardCompact: React.FC<ClassCardProps> = ({ 
  classItem, 
  onClick 
}) => {
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
    });
  };

  return (
    <Card 
      className={`
        hover:shadow-md transition-shadow cursor-pointer
        ${!classItem.isActive ? 'opacity-75' : ''}
      `}
      onClick={() => onClick?.(classItem)}
    >
      <CardContent className="p-4">
        <div className="flex justify-between items-start gap-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-sm line-clamp-1 mb-1">
              {classItem.name}
            </h3>
            <p className="text-xs text-gray-500 line-clamp-2 mb-2">
              {classItem.description}
            </p>
            <div className="flex items-center gap-3 text-xs text-gray-600">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {formatDate(classItem.startDate)}
              </span>
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {formatTime(classItem.startDate)}
              </span>
            </div>
          </div>
          
          <div className="flex flex-col items-end gap-2">
            <Badge 
              variant={classItem.isActive ? 'default' : 'secondary'}
              className="text-xs"
            >
              {classItem.isActive ? 'Aktif' : 'Nonaktif'}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {classItem.category}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
