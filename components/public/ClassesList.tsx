// 🎨 Komponen UI untuk Classes List
// Menampilkan daftar classes dengan loading, error, dan empty states

'use client';

import React from 'react';
import { usePublicClassesWithErrorHandling } from '@/lib/hooks/queries/use-public-classes-queries';
import { PublicClassDTO, ClassesListProps } from '@/types/public-api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Loader2, 
  AlertCircle, 
  RefreshCw, 
  Calendar,
  Clock,
  Tag,
  Users
} from 'lucide-react';

// Loading Skeleton Component
const ClassCardSkeleton = () => (
  <Card className="hover:shadow-lg transition-shadow">
    <CardHeader>
      <Skeleton className="h-6 w-3/4" />
    </CardHeader>
    <CardContent>
      <Skeleton className="h-4 w-full mb-4" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-4 w-2/3" />
        <Skeleton className="h-4 w-1/3" />
        <Skeleton className="h-6 w-20" />
      </div>
    </CardContent>
  </Card>
);

// Class Card Component
interface ClassCardProps {
  classItem: PublicClassDTO;
  onClick?: (classItem: PublicClassDTO) => void;
  showActions?: boolean;
}

const ClassCard: React.FC<ClassCardProps> = ({ 
  classItem, 
  onClick, 
  showActions = false 
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card 
      className={`hover:shadow-lg transition-shadow ${onClick ? 'cursor-pointer' : ''}`}
      onClick={() => onClick?.(classItem)}
    >
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg line-clamp-2">{classItem.name}</CardTitle>
          <Badge 
            variant={classItem.isActive ? 'default' : 'secondary'}
            className={classItem.isActive ? 'bg-green-100 text-green-800' : ''}
          >
            {classItem.isActive ? 'Aktif' : 'Tidak Aktif'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {classItem.description}
        </p>
        
        <div className="space-y-3 text-sm">
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-gray-500" />
            <span className="font-medium">Kategori:</span>
            <span>{classItem.category}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="font-medium">Tanggal:</span>
            <span>{formatDate(classItem.startDate)}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-gray-500" />
            <span className="font-medium">Waktu:</span>
            <span>
              {formatTime(classItem.startDate)} - {formatTime(classItem.endDate)}
            </span>
          </div>
        </div>

        {showActions && (
          <div className="mt-4 pt-4 border-t">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={(e) => {
                e.stopPropagation();
                onClick?.(classItem);
              }}
            >
              <Users className="h-4 w-4 mr-2" />
              Lihat Detail
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Main Classes List Component
export const ClassesList: React.FC<ClassesListProps> = ({ 
  tenantId = '1', 
  limit = 10,
  showPagination = false,
  showFilters = false,
  onClassSelect
}) => {
  const {
    data: classes,
    isLoading,
    isError,
    errorMessage,
    isUnauthorized,
    isNetworkError,
    hasData,
    retry,
  } = usePublicClassesWithErrorHandling({ tenantId, limit });

  // Loading State
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-3 text-gray-600">Memuat data classes...</span>
        </div>
        
        {/* Loading Skeletons */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <ClassCardSkeleton key={index} />
          ))}
        </div>
      </div>
    );
  }

  // Error States
  if (isError) {
    const getErrorIcon = () => {
      if (isUnauthorized) return <AlertCircle className="h-8 w-8 text-red-500" />;
      if (isNetworkError) return <RefreshCw className="h-8 w-8 text-orange-500" />;
      return <AlertCircle className="h-8 w-8 text-red-500" />;
    };

    const getErrorTitle = () => {
      if (isUnauthorized) return 'API Key Tidak Valid';
      if (isNetworkError) return 'Koneksi Bermasalah';
      return 'Terjadi Kesalahan';
    };

    const getErrorDescription = () => {
      if (isUnauthorized) return 'Silakan periksa konfigurasi API key Anda.';
      if (isNetworkError) return 'Periksa koneksi internet Anda dan coba lagi.';
      return 'Terjadi kesalahan saat memuat data. Silakan coba lagi.';
    };

    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-8 text-center">
          <div className="flex flex-col items-center space-y-4">
            {getErrorIcon()}
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-red-800">
                {getErrorTitle()}
              </h3>
              <p className="text-red-600">
                {getErrorDescription()}
              </p>
              {errorMessage && (
                <p className="text-sm text-red-500 font-mono bg-red-100 p-2 rounded">
                  {errorMessage}
                </p>
              )}
            </div>
            
            <Button 
              variant="outline" 
              onClick={() => retry()}
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Empty State
  if (!hasData) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <div className="flex flex-col items-center space-y-4">
            <Users className="h-16 w-16 text-gray-300" />
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-700">
                Belum Ada Classes
              </h3>
              <p className="text-gray-500">
                Belum ada classes tersedia untuk tenant ini.
              </p>
            </div>
            <Button 
              variant="outline" 
              onClick={() => retry()}
              className="mt-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Success State
  return (
    <div className="space-y-6">
      {/* Header dengan info */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Classes</h2>
          <p className="text-gray-600">
            Ditemukan {classes?.length || 0} classes
          </p>
        </div>
        
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => retry()}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Classes Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {classes?.map((classItem) => (
          <ClassCard 
            key={classItem.id} 
            classItem={classItem}
            onClick={onClassSelect}
            showActions={!!onClassSelect}
          />
        ))}
      </div>

      {/* Pagination placeholder */}
      {showPagination && (
        <div className="flex justify-center mt-8">
          <p className="text-gray-500 text-sm">
            Pagination akan diimplementasikan di versi selanjutnya
          </p>
        </div>
      )}
    </div>
  );
};
