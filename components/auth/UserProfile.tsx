'use client';

import { useState } from 'react';
import { User, LogOut, Settings, Calendar, CreditCard, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { sanitizeCustomerData, sanitizeUrl, escapeHtml } from '@/lib/utils/sanitization';

interface UserProfileProps {
  variant?: 'default' | 'compact' | 'minimal';
  showDropdown?: boolean;
}

export function UserProfile({ 
  variant = 'default', 
  showDropdown = true 
}: UserProfileProps) {
  const { isAuthenticated, customer, signOut, isLoading } = useAuth();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await signOut();
      toast.success('Signed out successfully');
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    } finally {
      setIsSigningOut(false);
    }
  };

  const handleSignIn = () => {
    router.push('/auth/signin');
  };

  const getInitials = (firstName?: string, lastName?: string, email?: string) => {
    // Sanitize inputs before processing
    const safeFirstName = escapeHtml(firstName);
    const safeLastName = escapeHtml(lastName);
    const safeEmail = escapeHtml(email);

    if (safeFirstName && safeLastName) {
      return `${safeFirstName[0]}${safeLastName[0]}`.toUpperCase();
    }
    if (safeEmail) {
      return safeEmail.substring(0, 2).toUpperCase();
    }
    return 'U';
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
        {variant !== 'minimal' && (
          <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
        )}
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    if (variant === 'minimal') {
      return (
        <Button
          onClick={handleSignIn}
          size="sm"
          className="bg-teal-600 hover:bg-teal-700"
        >
          Sign In
        </Button>
      );
    }

    return (
      <div className="flex items-center space-x-2">
        <Button
          onClick={handleSignIn}
          variant="outline"
          size="sm"
        >
          Sign In
        </Button>
        <Button
          onClick={() => router.push('/auth/signup')}
          size="sm"
          className="bg-teal-600 hover:bg-teal-700"
        >
          Sign Up
        </Button>
      </div>
    );
  }

  // Authenticated user - sanitize customer data
  const sanitizedCustomer = customer ? sanitizeCustomerData(customer) : null;
  const userInitials = getInitials(sanitizedCustomer?.firstName, sanitizedCustomer?.lastName, sanitizedCustomer?.email);
  const displayName = escapeHtml(sanitizedCustomer?.displayName) ||
                     `${escapeHtml(sanitizedCustomer?.firstName) || ''} ${escapeHtml(sanitizedCustomer?.lastName) || ''}`.trim() ||
                     escapeHtml(sanitizedCustomer?.email) || 'User';
  const safeImageUrl = sanitizeUrl(sanitizedCustomer?.image);

  if (variant === 'minimal') {
    return (
      <Avatar className="h-8 w-8">
        <AvatarImage src={safeImageUrl} alt={displayName} />
        <AvatarFallback className="bg-teal-600 text-white text-sm">
          {userInitials}
        </AvatarFallback>
      </Avatar>
    );
  }

  if (variant === 'compact') {
    return (
      <div className="flex items-center space-x-2">
        <Avatar className="h-8 w-8">
          <AvatarImage src={customer?.image} alt={displayName} />
          <AvatarFallback className="bg-teal-600 text-white text-sm">
            {userInitials}
          </AvatarFallback>
        </Avatar>
        <span className="text-sm font-medium text-gray-700 truncate max-w-24">
          {customer?.firstName || 'User'}
        </span>
      </div>
    );
  }

  // Default variant with dropdown
  if (!showDropdown) {
    return (
      <div className="flex items-center space-x-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={customer?.image} alt={displayName} />
          <AvatarFallback className="bg-teal-600 text-white">
            {userInitials}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-900">
            {displayName}
          </span>
          <span className="text-xs text-gray-500">
            {customer?.email}
          </span>
        </div>
        {customer?.membershipType && (
          <Badge variant="secondary" className="text-xs">
            {customer.membershipType}
          </Badge>
        )}
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center space-x-2 h-auto p-2 hover:bg-gray-50"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={customer?.image} alt={displayName} />
            <AvatarFallback className="bg-teal-600 text-white text-sm">
              {userInitials}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start">
            <span className="text-sm font-medium text-gray-900">
              {customer?.firstName || 'User'}
            </span>
            {customer?.membershipType && (
              <Badge variant="secondary" className="text-xs">
                {customer.membershipType}
              </Badge>
            )}
          </div>
          <ChevronDown className="h-4 w-4 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <span className="font-medium">{displayName}</span>
            <span className="text-xs text-gray-500 font-normal">
              {customer?.email}
            </span>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => router.push('/profile')}>
          <User className="h-4 w-4 mr-2" />
          Profile
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => router.push('/bookings')}>
          <Calendar className="h-4 w-4 mr-2" />
          My Bookings
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => router.push('/billing')}>
          <CreditCard className="h-4 w-4 mr-2" />
          Billing
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => router.push('/settings')}>
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleSignOut}
          disabled={isSigningOut}
          className="text-red-600 focus:text-red-600"
        >
          <LogOut className="h-4 w-4 mr-2" />
          {isSigningOut ? 'Signing out...' : 'Sign out'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Quick sign in/out button
export function QuickAuthButton() {
  const { isAuthenticated, signOut, isLoading } = useAuth();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await signOut();
      toast.success('Signed out successfully');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    } finally {
      setIsSigningOut(false);
    }
  };

  if (isLoading) {
    return (
      <Button size="sm" disabled>
        Loading...
      </Button>
    );
  }

  if (!isAuthenticated) {
    return (
      <Button
        onClick={() => router.push('/auth/signin')}
        size="sm"
        className="bg-teal-600 hover:bg-teal-700"
      >
        Sign In
      </Button>
    );
  }

  return (
    <Button
      onClick={handleSignOut}
      disabled={isSigningOut}
      size="sm"
      variant="outline"
    >
      <LogOut className="h-4 w-4 mr-2" />
      {isSigningOut ? 'Signing out...' : 'Sign Out'}
    </Button>
  );
}

// Authentication status indicator
export function AuthStatus() {
  const { isAuthenticated, customer, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-2 w-2 bg-gray-400 rounded-full animate-pulse"></div>
        <span className="text-xs text-gray-500">Checking...</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <div className={`h-2 w-2 rounded-full ${
        isAuthenticated ? 'bg-green-500' : 'bg-gray-400'
      }`}></div>
      <span className="text-xs text-gray-500">
        {isAuthenticated ? `Signed in as ${customer?.firstName || 'User'}` : 'Not signed in'}
      </span>
    </div>
  );
}
