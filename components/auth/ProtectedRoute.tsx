'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/contexts/AuthContext';
import { AuthLoading } from './AuthLoading';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

export function ProtectedRoute({ 
  children, 
  fallback,
  redirectTo = '/auth/signin',
  requireAuth = true 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, requireAuth, redirectTo, router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return fallback || <AuthLoading message="Checking authentication..." showCard />;
  }

  // If authentication is required but user is not authenticated, show loading
  // (redirect will happen in useEffect)
  if (requireAuth && !isAuthenticated) {
    return fallback || <AuthLoading message="Redirecting to sign in..." showCard />;
  }

  // If authentication is not required or user is authenticated, show children
  return <>{children}</>;
}

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  onUnauthenticated?: () => void;
}

export function AuthGuard({ 
  children, 
  fallback,
  onUnauthenticated 
}: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && !isAuthenticated && onUnauthenticated) {
      onUnauthenticated();
    }
  }, [isAuthenticated, isLoading, onUnauthenticated]);

  // Show loading state while checking authentication
  if (isLoading) {
    return fallback || <AuthLoading message="Checking authentication..." />;
  }

  // If user is not authenticated, show fallback or nothing
  if (!isAuthenticated) {
    return fallback || null;
  }

  // User is authenticated, show children
  return <>{children}</>;
}

interface ConditionalAuthProps {
  children: ReactNode;
  authenticated?: ReactNode;
  unauthenticated?: ReactNode;
  loading?: ReactNode;
}

export function ConditionalAuth({ 
  children,
  authenticated,
  unauthenticated,
  loading 
}: ConditionalAuthProps) {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return loading || <AuthLoading size="sm" />;
  }

  if (isAuthenticated && authenticated) {
    return <>{authenticated}</>;
  }

  if (!isAuthenticated && unauthenticated) {
    return <>{unauthenticated}</>;
  }

  return <>{children}</>;
}

interface BookingProtectedProps {
  children: ReactNode;
  scheduleId: string;
  onAuthRequired?: () => void;
}

export function BookingProtected({ 
  children, 
  scheduleId,
  onAuthRequired 
}: BookingProtectedProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  const handleAuthRequired = () => {
    // Store booking context
    sessionStorage.setItem('booking_context', JSON.stringify({
      scheduleId,
      timestamp: Date.now(),
    }));

    if (onAuthRequired) {
      onAuthRequired();
    } else {
      router.push('/auth/signin');
    }
  };

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      handleAuthRequired();
    }
  }, [isAuthenticated, isLoading]);

  // Show loading state while checking authentication
  if (isLoading) {
    return <AuthLoading message="Checking authentication..." size="sm" />;
  }

  // If user is not authenticated, show loading (redirect will happen)
  if (!isAuthenticated) {
    return <AuthLoading message="Redirecting to sign in..." size="sm" />;
  }

  // User is authenticated, show children
  return <>{children}</>;
}

interface RoleProtectedProps {
  children: ReactNode;
  allowedRoles?: string[];
  fallback?: ReactNode;
}

export function RoleProtected({ 
  children, 
  allowedRoles = [],
  fallback 
}: RoleProtectedProps) {
  const { isAuthenticated, customer, isLoading } = useAuth();

  // Show loading state while checking authentication
  if (isLoading) {
    return fallback || <AuthLoading message="Checking permissions..." size="sm" />;
  }

  // If user is not authenticated, don't show anything
  if (!isAuthenticated || !customer) {
    return fallback || null;
  }

  // If no roles specified, just check authentication
  if (allowedRoles.length === 0) {
    return <>{children}</>;
  }

  // Check if user has required role (you might need to add role field to Customer type)
  // For now, we'll assume all authenticated users have access
  const hasRequiredRole = true; // Implement role checking logic here

  if (!hasRequiredRole) {
    return fallback || null;
  }

  return <>{children}</>;
}
