'use client';

import { Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface AuthLoadingProps {
  message?: string;
  showCard?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function AuthLoading({ 
  message = 'Loading...', 
  showCard = false,
  size = 'md' 
}: AuthLoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const content = (
    <div className="flex items-center justify-center space-x-2">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-teal-600`} />
      <span className={`${textSizeClasses[size]} text-gray-600`}>
        {message}
      </span>
    </div>
  );

  if (showCard) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="p-8">
            {content}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      {content}
    </div>
  );
}

export function InlineAuthLoading({ 
  message = 'Loading...', 
  size = 'sm' 
}: Omit<AuthLoadingProps, 'showCard'>) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <div className="flex items-center space-x-2">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-teal-600`} />
      <span className={`${textSizeClasses[size]} text-gray-600`}>
        {message}
      </span>
    </div>
  );
}

export function AuthLoadingSkeleton() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md shadow-lg">
        <div className="p-6 space-y-6">
          {/* Header skeleton */}
          <div className="text-center space-y-2">
            <div className="h-8 bg-gray-200 rounded animate-pulse mx-auto w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse mx-auto w-1/2"></div>
          </div>

          {/* Button skeleton */}
          <div className="h-12 bg-gray-200 rounded animate-pulse"></div>

          {/* Separator */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
            </div>
          </div>

          {/* Form skeleton */}
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
              <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
              <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
          </div>

          {/* Footer skeleton */}
          <div className="text-center">
            <div className="h-4 bg-gray-200 rounded animate-pulse mx-auto w-2/3"></div>
          </div>
        </div>
      </Card>
    </div>
  );
}

export function ButtonLoading({ 
  children, 
  isLoading, 
  loadingText = 'Loading...',
  ...props 
}: {
  children: React.ReactNode;
  isLoading: boolean;
  loadingText?: string;
  [key: string]: any;
}) {
  return (
    <button {...props} disabled={isLoading || props.disabled}>
      {isLoading ? (
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>{loadingText}</span>
        </div>
      ) : (
        children
      )}
    </button>
  );
}
