'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw, Home, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Store error info in state
    this.setState({
      errorInfo,
    });
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // Report error to monitoring service (if available)
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // In a real app, you would send this to your error monitoring service
      // like Sentry, LogRocket, or Bugsnag
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };
      
      console.error('Error Report:', errorReport);
      
      // Example: Send to monitoring service
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport),
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-6">
            <Card className="shadow-lg">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4">
                  <AlertTriangle className="h-12 w-12 text-red-600" />
                </div>
                <CardTitle className="text-2xl text-red-600">
                  Oops! Something went wrong
                </CardTitle>
                <CardDescription>
                  We encountered an unexpected error. Our team has been notified.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {this.state.errorId && (
                  <Alert>
                    <Bug className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Error ID:</strong> {this.state.errorId}
                      <br />
                      <span className="text-xs text-gray-500">
                        Please include this ID when contacting support.
                      </span>
                    </AlertDescription>
                  </Alert>
                )}
                
                {this.props.showDetails && this.state.error && (
                  <Alert variant="destructive">
                    <AlertDescription>
                      <strong>Error:</strong> {this.state.error.message}
                      {this.state.error.stack && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm">
                            Technical Details
                          </summary>
                          <pre className="mt-2 text-xs overflow-auto max-h-32 bg-gray-100 p-2 rounded">
                            {this.state.error.stack}
                          </pre>
                        </details>
                      )}
                    </AlertDescription>
                  </Alert>
                )}
                
                <div className="space-y-2">
                  <Button 
                    onClick={this.handleRetry}
                    className="w-full bg-teal-600 hover:bg-teal-700"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      onClick={this.handleGoHome}
                      variant="outline"
                      className="w-full"
                    >
                      <Home className="h-4 w-4 mr-2" />
                      Go Home
                    </Button>
                    
                    <Button 
                      onClick={this.handleReload}
                      variant="outline"
                      className="w-full"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Reload
                    </Button>
                  </div>
                </div>
                
                <div className="text-center">
                  <p className="text-xs text-gray-500">
                    If the problem persists, please{' '}
                    <a 
                      href="/support" 
                      className="text-teal-600 hover:text-teal-500"
                    >
                      contact support
                    </a>
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by useErrorHandler:', error, errorInfo);
    
    // In a real app, report to error monitoring service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
    
    console.error('Error Report:', errorReport);
  };
}

// Simple error fallback component
export function ErrorFallback({ 
  error, 
  resetError 
}: { 
  error: Error; 
  resetError: () => void; 
}) {
  return (
    <div className="min-h-[200px] flex items-center justify-center bg-gray-50 rounded-lg border border-gray-200">
      <div className="text-center space-y-4 p-6">
        <AlertTriangle className="h-8 w-8 text-red-600 mx-auto" />
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            Something went wrong
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            {error.message}
          </p>
        </div>
        <Button 
          onClick={resetError}
          size="sm"
          className="bg-teal-600 hover:bg-teal-700"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Try again
        </Button>
      </div>
    </div>
  );
}

// Auth-specific error boundary
export function AuthErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <CardTitle className="text-xl text-red-600">
                Authentication Error
              </CardTitle>
              <CardDescription>
                There was a problem with the authentication system.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={() => window.location.href = '/auth/signin'}
                className="w-full bg-teal-600 hover:bg-teal-700"
              >
                Go to Sign In
              </Button>
            </CardContent>
          </Card>
        </div>
      }
      onError={(error, errorInfo) => {
        console.error('Auth Error:', error, errorInfo);
        // Report auth-specific errors
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
