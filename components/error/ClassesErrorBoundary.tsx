// 🚨 Error Boundary untuk Classes
// Comprehensive error handling dengan user feedback yang baik

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Wifi, 
  Shield,
  Home
} from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface ClassesErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
}

interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
  errorInfo?: React.ErrorInfo;
}

// Default Error Fallback Component
const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetError, 
  errorInfo 
}) => {
  const getErrorType = (error: Error) => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('unauthorized') || message.includes('api key')) {
      return 'auth';
    }
    if (message.includes('not found')) {
      return 'notfound';
    }
    return 'unknown';
  };

  const getErrorIcon = (type: string) => {
    switch (type) {
      case 'network':
        return <Wifi className="h-12 w-12 text-orange-500" />;
      case 'auth':
        return <Shield className="h-12 w-12 text-red-500" />;
      case 'notfound':
        return <AlertTriangle className="h-12 w-12 text-yellow-500" />;
      default:
        return <Bug className="h-12 w-12 text-red-500" />;
    }
  };

  const getErrorTitle = (type: string) => {
    switch (type) {
      case 'network':
        return 'Koneksi Bermasalah';
      case 'auth':
        return 'Masalah Autentikasi';
      case 'notfound':
        return 'Data Tidak Ditemukan';
      default:
        return 'Terjadi Kesalahan';
    }
  };

  const getErrorDescription = (type: string) => {
    switch (type) {
      case 'network':
        return 'Periksa koneksi internet Anda dan coba lagi.';
      case 'auth':
        return 'API key tidak valid atau sudah expired. Hubungi administrator.';
      case 'notfound':
        return 'Data yang diminta tidak ditemukan atau sudah dihapus.';
      default:
        return 'Terjadi kesalahan yang tidak terduga. Tim kami akan segera memperbaikinya.';
    }
  };

  const errorType = getErrorType(error);

  const handleReportError = () => {
    // Send error report to monitoring service
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: false,
        custom_map: {
          error_type: errorType,
          component: 'ClassesErrorBoundary',
          stack: error.stack?.substring(0, 500),
        },
      });
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Report');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Stack:', error.stack);
      console.groupEnd();
    }
  };

  React.useEffect(() => {
    handleReportError();
  }, []);

  return (
    <div className="min-h-[400px] flex items-center justify-center p-6">
      <Card className="w-full max-w-md border-red-200">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getErrorIcon(errorType)}
          </div>
          <CardTitle className="text-xl text-gray-900">
            {getErrorTitle(errorType)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-gray-600">
            {getErrorDescription(errorType)}
          </p>

          {/* Error Details (Development Only) */}
          {process.env.NODE_ENV === 'development' && (
            <Alert>
              <Bug className="h-4 w-4" />
              <AlertDescription className="text-xs font-mono">
                {error.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col gap-2">
            <Button onClick={resetError} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/'}
              className="w-full"
            >
              <Home className="h-4 w-4 mr-2" />
              Kembali ke Beranda
            </Button>
          </div>

          {/* Support Info */}
          <div className="text-center text-xs text-gray-500 pt-4 border-t">
            <p>Jika masalah berlanjut, hubungi support:</p>
            <p className="font-mono"><EMAIL></p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Error Boundary Class Component
export class ClassesErrorBoundary extends React.Component<
  ClassesErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ClassesErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 Classes Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Report to error monitoring service
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: true,
        custom_map: {
          component: 'ClassesErrorBoundary',
          stack: error.stack?.substring(0, 500),
          component_stack: errorInfo.componentStack?.substring(0, 500),
        },
      });
    }
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      
      return (
        <FallbackComponent
          error={this.state.error}
          resetError={this.resetError}
          errorInfo={this.state.errorInfo || undefined}
        />
      );
    }

    return this.props.children;
  }
}

// Hook untuk error handling di functional components
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    console.error('🚨 Error handled by useErrorHandler:', error);
    setError(error);
  }, []);

  // Throw error to be caught by Error Boundary
  if (error) {
    throw error;
  }

  return { handleError, resetError };
};

// HOC untuk wrapping components dengan Error Boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<ErrorFallbackProps>
) => {
  const WrappedComponent = (props: P) => (
    <ClassesErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ClassesErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};
