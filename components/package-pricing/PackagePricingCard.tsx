import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Star } from 'lucide-react';
import { PackagePricing } from '@/types/package-pricing';
import { cn } from '@/lib/utils';

interface PackagePricingCardProps {
  package: PackagePricing;
  isRecommended?: boolean;
  onSelect?: (packageId: string) => void;
  className?: string;
}

export function PackagePricingCard({ 
  package: pkg, 
  isRecommended = false, 
  onSelect, 
  className 
}: PackagePricingCardProps) {
  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getPackageFeatures = (pkg: PackagePricing): string[] => {
    const features = [];
    
    if (pkg.creditAmount > 0) {
      features.push(`${pkg.creditAmount} credits included`);
    }
    
    if (pkg.packageDescription) {
      features.push(pkg.packageDescription);
    }
    
    // Add some default features based on package type
    if (pkg.packageName.toLowerCase().includes('basic')) {
      features.push('Access to group classes');
      features.push('Online booking system');
      features.push('Mobile app access');
    } else if (pkg.packageName.toLowerCase().includes('premium')) {
      features.push('Priority booking');
      features.push('Access to all classes');
      features.push('Personal trainer consultations');
      features.push('Nutrition guidance');
    }
    
    features.push('Flexible scheduling');
    features.push('Cancel up to 2 hours before');
    
    return features;
  };

  const features = getPackageFeatures(pkg);

  return (
    <Card className={cn(
      "relative h-full transition-all duration-300 hover:shadow-lg",
      "border-2 hover:border-teal-300",
      isRecommended && "border-teal-500 shadow-md",
      className
    )}>
      {isRecommended && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-teal-600 text-white px-3 py-1 flex items-center space-x-1">
            <Star className="h-3 w-3 fill-current" />
            <span>Recommended</span>
          </Badge>
        </div>
      )}

      <CardHeader className="text-center pb-4">
        <CardTitle className="text-xl font-bold text-gray-900">
          {pkg.packageName}
        </CardTitle>
        <CardDescription className="text-gray-600 mt-2">
          {pkg.packageDescription || 'Complete wellness package for your fitness journey'}
        </CardDescription>
        
        <div className="mt-4">
          <div className="text-3xl font-bold text-teal-700">
            {formatPrice(pkg.price, pkg.currency)}
          </div>
          {pkg.creditAmount > 0 && (
            <div className="text-sm text-gray-600 mt-1">
              {pkg.creditAmount} credits • {pkg.currency}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start space-x-3">
              <Check className="h-4 w-4 text-teal-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-gray-700">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>

      <CardFooter className="pt-6">
        <Button
          onClick={() => onSelect?.(pkg.id)}
          className={cn(
            "w-full font-medium transition-all duration-200",
            isRecommended
              ? "bg-teal-600 hover:bg-teal-700 text-white"
              : "bg-white border-2 border-teal-600 text-teal-600 hover:bg-teal-50"
          )}
          size="lg"
        >
          {isRecommended ? 'Get Started' : 'Choose Package'}
        </Button>
      </CardFooter>
    </Card>
  );
}
