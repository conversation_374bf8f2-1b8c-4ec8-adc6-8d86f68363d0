import React from 'react';
import { cn } from '@/lib/utils';
import { Calendar, Package } from 'lucide-react';

export type TabType = 'schedules' | 'packages';

interface TabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  className?: string;
}

interface TabItem {
  id: TabType;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const tabs: TabItem[] = [
  {
    id: 'schedules',
    label: 'Schedules',
    icon: Calendar,
    description: 'Browse and book class schedules',
  },
  {
    id: 'packages',
    label: 'Package Pricing',
    icon: Package,
    description: 'View available packages and pricing',
  },
];

export function TabNavigation({ activeTab, onTabChange, className }: TabNavigationProps) {
  return (
    <div className={cn("bg-white/90 backdrop-blur-md border-b border-teal-200 sticky top-0 z-50", className)}>
      <div className="container mx-auto px-4">
        <nav className="flex space-x-8" role="tablist">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={cn(
                  "flex items-center space-x-2 py-4 px-2 text-sm font-medium transition-all duration-200",
                  "border-b-2 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2",
                  "hover:text-teal-700 hover:border-teal-300",
                  isActive
                    ? "text-teal-700 border-teal-600 bg-teal-50/50"
                    : "text-gray-600 border-transparent"
                )}
                role="tab"
                aria-selected={isActive}
                aria-controls={`${tab.id}-panel`}
                id={`${tab.id}-tab`}
              >
                <Icon className={cn(
                  "h-4 w-4 transition-colors",
                  isActive ? "text-teal-600" : "text-gray-500"
                )} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
