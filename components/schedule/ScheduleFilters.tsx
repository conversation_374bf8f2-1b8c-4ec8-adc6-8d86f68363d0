import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScheduleFilters as FilterType } from '@/types/schedule';
import { cn } from '@/lib/utils';

interface ScheduleFiltersProps {
  filters: FilterType;
  onFiltersChange: (filters: Partial<FilterType>) => void;
  className?: string;
}

export function ScheduleFilters({ filters, onFiltersChange, className = '' }: ScheduleFiltersProps) {
  const handleSelectChange = (field: keyof FilterType, value: string) => {
    const parsedValue = value === 'all' ? undefined : value;
    onFiltersChange({ [field]: parsedValue });
  };

  const selectTriggerClass = "bg-teal-700/60 border-teal-600/50 text-white placeholder:text-teal-200 hover:bg-teal-700/70 focus:ring-teal-500 focus:border-teal-500 transition-all duration-200";

  return (
    <div className={cn("grid grid-cols-2 md:grid-cols-5 gap-3", className)}>
      <Select
        value={filters.search || 'all'}
        onValueChange={(value) => handleSelectChange('search', value)}
      >
        <SelectTrigger className={selectTriggerClass}>
          <SelectValue placeholder="Search" />
        </SelectTrigger>
        <SelectContent className="bg-white border-teal-200">
          <SelectItem value="all">All</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={filters.locationId || 'all'}
        onValueChange={(value) => handleSelectChange('locationId', value)}
      >
        <SelectTrigger className={selectTriggerClass}>
          <SelectValue placeholder="Location" />
        </SelectTrigger>
        <SelectContent className="bg-white border-teal-200">
          <SelectItem value="all">All Locations</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={filters.isPrivate === undefined ? 'all' : filters.isPrivate ? 'private' : 'group'}
        onValueChange={(value) => handleSelectChange('isPrivate', value === 'private' ? 'true' : value === 'group' ? 'false' : value)}
      >
        <SelectTrigger className={selectTriggerClass}>
          <SelectValue placeholder="Classes" />
        </SelectTrigger>
        <SelectContent className="bg-white border-teal-200">
          <SelectItem value="all">All Classes</SelectItem>
          <SelectItem value="group">Group Classes</SelectItem>
          <SelectItem value="private">Private Sessions</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={filters.staffId || 'all'}
        onValueChange={(value) => handleSelectChange('staffId', value)}
      >
        <SelectTrigger className={selectTriggerClass}>
          <SelectValue placeholder="Staff" />
        </SelectTrigger>
        <SelectContent className="bg-white border-teal-200">
          <SelectItem value="all">All Staff</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={filters.allowClasspass === undefined ? 'all' : filters.allowClasspass ? 'yes' : 'no'}
        onValueChange={(value) => handleSelectChange('allowClasspass', value === 'yes' ? 'true' : value === 'no' ? 'false' : value)}
      >
        <SelectTrigger className={selectTriggerClass}>
          <SelectValue placeholder="ClassPass" />
        </SelectTrigger>
        <SelectContent className="bg-white border-teal-200">
          <SelectItem value="all">All</SelectItem>
          <SelectItem value="yes">ClassPass Accepted</SelectItem>
          <SelectItem value="no">ClassPass Not Accepted</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
