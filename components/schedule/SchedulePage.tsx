import React, { useCallback, useState, useMemo } from 'react';
import { RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useSchedules } from '@/lib/hooks/useSchedules';
import { useIntersectionObserver } from '@/lib/hooks/useIntersectionObserver';
import { TabNavigation, TabType } from '@/components/navigation/TabNavigation';
import { PackagePricingPage } from '@/components/package-pricing/PackagePricingPage';
import { ScheduleFilters } from './ScheduleFilters';
import { ScheduleSkeleton } from './ScheduleSkeleton';
import { ScheduleEmpty } from './ScheduleEmpty';
import { DateNavigation } from './DateNavigation';
import { ScheduleGroup } from './ScheduleGroup';
import { ClassSchedule } from '@/types/schedule';
import { toast } from 'sonner';
import { getSchedulesForDate } from '@/lib/utils/schedule';

interface SchedulePageProps {
  tenantId?: number;
  className?: string;
}

export function SchedulePage({ tenantId = 1, className = '' }: SchedulePageProps) {
  const [activeTab, setActiveTab] = useState<TabType>('schedules');
  const [selectedDate, setSelectedDate] = useState(new Date());

  const {
    schedules,
    loading,
    error,
    hasMore,
    filters,
    refetch,
    loadMore,
    updateFilters,
    reset,
  } = useSchedules({
    initialFilters: { tenantId },
    enabled: activeTab === 'schedules',
  });

  // Infinite scroll
  const { elementRef, isIntersecting } = useIntersectionObserver({
    enabled: hasMore && !loading && activeTab === 'schedules',
  });

  React.useEffect(() => {
    if (isIntersecting && hasMore && !loading && activeTab === 'schedules') {
      loadMore();
    }
  }, [isIntersecting, hasMore, loading, loadMore, activeTab]);

  // Filter schedules by selected date
  const filteredSchedules = useMemo(() => {
    return getSchedulesForDate(schedules, selectedDate);
  }, [schedules, selectedDate]);

  const handleBookSchedule = useCallback((schedule: ClassSchedule) => {
    toast.success(`Booking initiated for schedule ${schedule.id}`);
    console.log('Booking schedule:', schedule);
  }, []);

  const handleRefresh = useCallback(() => {
    refetch();
    toast.success('Schedules refreshed');
  }, [refetch]);

  const handleClearFilters = useCallback(() => {
    reset();
  }, [reset]);

  const hasActiveFilters = Boolean(
    filters.search || 
    filters.startDate || 
    filters.endDate || 
    filters.locationId || 
    filters.facilityId || 
    filters.staffId || 
    filters.isPrivate !== undefined || 
    filters.allowClasspass !== undefined
  );

  // Error state for schedules tab only
  if (error && activeTab === 'schedules') {
    return (
      <div className={`min-h-screen ${className}`}>
        <TabNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        <div className="min-h-screen bg-gradient-to-br from-teal-800 via-teal-700 to-teal-900">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center py-12">
              <h1 className="text-3xl font-bold text-white mb-4">Class Schedules</h1>
              <p className="text-red-300 mb-6">{error}</p>
              <Button
                onClick={handleRefresh}
                className="bg-white text-teal-800 hover:bg-gray-100"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${className}`}>
      {/* Tab Navigation */}
      <TabNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Tab Content */}
      {activeTab === 'schedules' ? (
        <div className="min-h-screen bg-gradient-to-br from-teal-800 via-teal-700 to-teal-900">
          <div className="container mx-auto px-4 py-6 space-y-6">
            {/* Date Navigation */}
            <DateNavigation
              selectedDate={selectedDate}
              onDateSelect={setSelectedDate}
            />

            {/* Filters */}
            <ScheduleFilters
              filters={filters}
              onFiltersChange={updateFilters}
            />

            {/* Content */}
            <div className="space-y-6">
              {/* Loading State */}
              {loading && schedules.length === 0 && (
                <div className="space-y-4">
                  <ScheduleSkeleton count={filters.limit || 10} />
                </div>
              )}

              {/* Empty State */}
              {!loading && schedules.length === 0 && (
                <div className="text-center py-12">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
                    <h3 className="text-xl font-semibold text-white mb-2">No classes found</h3>
                    <p className="text-teal-200 mb-4">
                      {hasActiveFilters
                        ? 'Try adjusting your filters to see more classes'
                        : 'No classes are scheduled for the selected date'
                      }
                    </p>
                    {hasActiveFilters && (
                      <Button
                        onClick={handleClearFilters}
                        className="bg-white text-teal-800 hover:bg-gray-100"
                      >
                        Clear Filters
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {/* Schedules for Selected Date */}
              {filteredSchedules.length > 0 ? (
                <div className="space-y-4">
                  <ScheduleGroup
                    date={selectedDate.toISOString().split('T')[0]}
                    schedules={filteredSchedules}
                    onBook={handleBookSchedule}
                  />

                  {/* Infinite scroll trigger */}
                  {hasMore && (
                    <div ref={elementRef} className="h-4" />
                  )}
                </div>
              ) : (
                <div className="bg-white/10 backdrop-blur-sm rounded-lg">
                  <ScheduleEmpty
                    hasFilters={schedules.length > 0}
                    onRefresh={refetch}
                  />
                </div>
              )}

              {/* Loading state for infinite scroll */}
              {loading && hasMore && (
                <div className="space-y-3">
                  <ScheduleSkeleton count={3} />
                </div>
              )}

              {/* End of Results */}
              {!hasMore && schedules.length > 0 && (
                <div className="text-center py-8">
                  <p className="text-teal-200">You've reached the end of the results</p>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <PackagePricingPage />
      )}
    </div>
  );
}
