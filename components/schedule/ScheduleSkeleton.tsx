import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface ScheduleSkeletonProps {
  count?: number;
}

export function ScheduleSkeleton({ count = 3 }: ScheduleSkeletonProps) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }, (_, i) => (
        <div key={i} className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <Skeleton className="h-5 w-12 bg-white/20 mb-1" />
                <Skeleton className="h-3 w-8 bg-white/20" />
              </div>

              <div className="flex-1 space-y-2">
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-2 w-2 rounded-full bg-white/20" />
                  <Skeleton className="h-4 w-48 bg-white/20" />
                </div>
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-3 w-20 bg-white/20" />
                  <Skeleton className="h-3 w-24 bg-white/20" />
                </div>
              </div>
            </div>

            <Skeleton className="h-8 w-20 bg-white/20 rounded-full" />
          </div>
        </div>
      ))}
    </div>
  );
}