import React from 'react';
import { ClassSchedule } from '@/types/schedule';
import { ScheduleCard } from './ScheduleCard';
import { format, parseISO } from 'date-fns';
import { cn } from '@/lib/utils';

interface ScheduleGroupProps {
  date: string;
  schedules: ClassSchedule[];
  onBook: (schedule: ClassSchedule) => void;
  className?: string;
}

export function ScheduleGroup({ date, schedules, onBook, className }: ScheduleGroupProps) {
  const formatDateHeader = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      const dayName = format(date, 'EEEE');
      const dayNumber = format(date, 'd');
      const monthName = format(date, 'MMM');
      
      return { dayName, dayNumber, monthName };
    } catch (error) {
      return { dayName: 'Invalid', dayNumber: '0', monthName: 'Date' };
    }
  };

  const { dayName, dayNumber, monthName } = formatDateHeader(date);
  const scheduleCount = schedules.length;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Date Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-baseline space-x-2">
          <h2 className="text-white text-lg font-medium">
            {dayName}, {dayNumber} {monthName}
          </h2>
          <span className="text-teal-200 text-sm">
            {scheduleCount} {scheduleCount === 1 ? 'class' : 'classes'}
          </span>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-white text-sm">Booked</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center justify-center w-6 h-6 bg-orange-500 rounded-full">
              <span className="text-white text-xs font-bold">W</span>
            </div>
            <span className="text-white text-sm">Waitlisted</span>
          </div>
        </div>
      </div>

      {/* Schedule Cards */}
      <div className="space-y-3">
        {schedules.map((schedule) => (
          <ScheduleCard
            key={schedule.id}
            schedule={schedule}
            onBook={onBook}
            variant="compact"
          />
        ))}
      </div>
    </div>
  );
}
