import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Users, MapPin, User, Lock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ClassSchedule } from '@/types/schedule';
import { formatDateTime, formatDuration } from '@/lib/utils/date';
import {
  getScheduleDisplayName,
  getLocationDisplayText,
  getStaffDisplayText,
  getScheduleDisplayNameAsync,
  getLocationDisplayTextAsync,
  getStaffDisplayTextAsync
} from '@/lib/utils/schedule';
import { useScheduleData } from '@/lib/hooks/useScheduleData';
import { useAuth } from '@/lib/contexts/AuthContext';
import { sanitizeScheduleData, escapeHtml, sanitizeCssValue } from '@/lib/utils/sanitization';
import { useAuthForBooking } from '@/lib/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface ScheduleCardProps {
  schedule: ClassSchedule;
  onBook?: (schedule: ClassSchedule) => void;
  className?: string;
  variant?: 'default' | 'compact';
}

export const ScheduleCard = React.memo<ScheduleCardProps>(({
  schedule,
  onBook,
  className = '',
  variant = 'default',
}) => {
  const { getClassData, getLocationData, getFacilityData, getStaffData } = useScheduleData();
  const { isAuthenticated } = useAuth();
  const { requireAuth } = useAuthForBooking();

  // Sanitize schedule data to prevent XSS
  const sanitizedSchedule = sanitizeScheduleData(schedule);

  // State for real data
  const [displayName, setDisplayName] = useState<string>(escapeHtml(getScheduleDisplayName(sanitizedSchedule)));
  const [locationText, setLocationText] = useState<string | null>(getLocationDisplayText(sanitizedSchedule));
  const [staffText, setStaffText] = useState<string | null>(getStaffDisplayText(sanitizedSchedule));
  const [dataLoading, setDataLoading] = useState<boolean>(true);

  // Fetch real data on mount
  useEffect(() => {
    const fetchRealData = async () => {
      try {
        setDataLoading(true);

        // Fetch real class name
        const realDisplayName = await getScheduleDisplayNameAsync(schedule, getClassData);
        setDisplayName(realDisplayName);

        // Fetch real location/facility text
        const realLocationText = await getLocationDisplayTextAsync(
          schedule,
          getLocationData,
          getFacilityData
        );
        setLocationText(realLocationText);

        // Fetch real staff text
        const realStaffText = await getStaffDisplayTextAsync(schedule, getStaffData);
        setStaffText(realStaffText);

      } catch (error) {
        console.error('Error fetching real schedule data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    fetchRealData();
  }, [schedule.id, schedule.class_id, schedule.location_id, schedule.facility_id, schedule.staff_id]);

  const handleBook = () => {
    // Check if user is authenticated before booking
    if (!isAuthenticated) {
      const needsAuth = requireAuth(schedule.id, window.location.pathname);
      if (needsAuth) {
        toast.info('Please sign in to book this class');
        return;
      }
    }

    // User is authenticated, proceed with booking
    onBook?.(schedule);
  };

  const availableSpots = schedule.pax;
  const isFullyBooked = availableSpots === 0;
  const isPrivate = schedule.is_private;

  // Compact variant for the new design
  if (variant === 'compact') {
    return (
      <div className={cn(
        "bg-white/95 backdrop-blur-sm rounded-lg p-4 hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md",
        className
      )}>
        <div className="flex items-center justify-between">
          {/* Time and Duration */}
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {formatDateTime(schedule.start_time, 'time')}
              </div>
              <div className="text-xs text-gray-500">
                {formatDuration(schedule.duration)}
              </div>
            </div>

            {/* Class Info */}
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <div
                  className="h-2 w-2 rounded-full"
                  style={{ backgroundColor: sanitizeCssValue(sanitizedSchedule.calender_color) }}
                />
                <h3 className="font-medium text-gray-900">
                  {displayName}
                </h3>
                {schedule.allow_classpass && (
                  <Badge variant="outline" className="text-xs">
                    ClassPass
                  </Badge>
                )}
              </div>

              <div className="flex items-center space-x-4 text-sm text-gray-600">
                {staffText && (
                  <span>{staffText}</span>
                )}
                {locationText && (
                  <span>{locationText}</span>
                )}
              </div>
            </div>
          </div>

          {/* Book Button */}
          <Button
            onClick={handleBook}
            disabled={isFullyBooked}
            className={cn(
              "px-6 py-2 rounded-full text-sm font-medium transition-all duration-200",
              isFullyBooked
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : isAuthenticated
                ? 'bg-teal-600 text-white hover:bg-teal-700'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400'
            )}
          >
            {isFullyBooked ? 'Full' : isAuthenticated ? 'Book Now' : (
              <div className="flex items-center space-x-1">
                <Lock className="h-4 w-4" />
                <span>Sign in</span>
              </div>
            )}
          </Button>
        </div>
      </div>
    );
  }

  // Default variant (original design)
  return (
    <Card className={cn("overflow-hidden hover:shadow-lg transition-all duration-300", className)}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-3">
            {/* Status indicator */}
            <div className="flex items-center space-x-3">
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: schedule.calender_color }}
              />
              <Badge
                variant={isPrivate ? "secondary" : "default"}
                className="text-xs"
              >
                {isPrivate ? 'Private' : 'Group'}
              </Badge>
              {schedule.allow_classpass && (
                <Badge variant="outline" className="text-xs">
                  ClassPass
                </Badge>
              )}
            </div>

            {/* Class info */}
            <h3 className="text-lg font-semibold text-gray-900">
              {displayName}
              {dataLoading && (
                <span className="ml-2 text-xs text-gray-500 animate-pulse">Loading...</span>
              )}
            </h3>

            {/* Date and time */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDateTime(schedule.start_time, 'date')}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>
                  {formatDateTime(schedule.start_time, 'time')} - {formatDateTime(schedule.end_time, 'time')}
                </span>
              </div>
            </div>

            {/* Duration and capacity */}
            <div className="flex items-center space-x-4 text-sm">
              <Badge variant="outline">
                {formatDuration(schedule.duration)}
              </Badge>
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{availableSpots} spots</span>
              </div>
              {schedule.waitlist > 0 && (
                <Badge variant="secondary">
                  +{schedule.waitlist} waitlist
                </Badge>
              )}
            </div>

            {/* Location info */}
            {locationText && (
              <div className="flex items-center space-x-1 text-sm text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>{locationText}</span>
              </div>
            )}

            {/* Staff info */}
            {staffText && (
              <div className="flex items-center space-x-1 text-sm text-gray-600">
                <User className="h-4 w-4" />
                <span>{staffText}</span>
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col items-end space-y-2">
            <Button
              onClick={handleBook}
              disabled={isFullyBooked}
              className={`${isFullyBooked ? 'bg-gray-400' : 'bg-teal-600 hover:bg-teal-700'} text-white`}
            >
              {isFullyBooked ? 'Full' : isAuthenticated ? 'Book Now' : (
                <div className="flex items-center space-x-1">
                  <Lock className="h-4 w-4" />
                  <span>Sign in to Book</span>
                </div>
              )}
            </Button>
            {schedule.waitlist > 0 && isFullyBooked && (
              <Button variant="outline" size="sm">
                Join Waitlist
              </Button>
            )}
            <p className="text-xs text-gray-500">
              {availableSpots} {availableSpots === 1 ? 'spot' : 'spots'} left
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

ScheduleCard.displayName = 'ScheduleCard';



// 