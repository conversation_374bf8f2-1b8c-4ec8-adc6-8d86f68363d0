import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Users, MapPin, User, Lock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ClassSchedule } from '@/types/schedule';
import { formatDateTime, formatDuration } from '@/lib/utils/date';
import {
  getScheduleName,
  getScheduleDescription,
  getLocationDisplayText,
  getStaffDisplayText,
  getLocationDisplayTextAsync,
  getStaffDisplayTextAsync
} from '@/lib/utils/schedule';
import { useScheduleData } from '@/lib/hooks/useScheduleData';
import { useAuth } from '@/lib/contexts/AuthContext';
import { sanitizeScheduleData, escapeHtml, sanitizeCssValue } from '@/lib/utils/sanitization';
import { useAuthForBooking } from '@/lib/contexts/AuthContext';
import { useCustomerOAuth } from '@/lib/hooks/useCustomerOAuth';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface ScheduleCardProps {
  schedule: ClassSchedule;
  onBook?: (schedule: ClassSchedule) => void;
  className?: string;
  variant?: 'default' | 'compact';
}

export const ScheduleCard = React.memo<ScheduleCardProps>(({
  schedule,
  onBook,
  className = '',
  variant = 'default',
}) => {
  const { getClassData, getLocationData, getFacilityData, getStaffData } = useScheduleData();
  const { isAuthenticated, isLoading: isAuthLoading } = useAuth();
  const { requireAuth } = useAuthForBooking();
  const { initiateGoogleOAuth, isLoading: isOAuthLoading } = useCustomerOAuth();
  const [bookingLoading, setBookingLoading] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);

  // Sanitize schedule data to prevent XSS
  const sanitizedSchedule = sanitizeScheduleData(schedule);

  // Get data directly from schedule (no need for complex async fetching)
  const scheduleName = escapeHtml(getScheduleName(sanitizedSchedule));
  const scheduleDescription = getScheduleDescription(sanitizedSchedule);
  const locationText = getLocationDisplayText(sanitizedSchedule);
  const staffText = getStaffDisplayText(sanitizedSchedule);

  // State for enhanced data (optional async enhancement)
  const [enhancedLocationText, setEnhancedLocationText] = useState<string | null>(locationText);
  const [enhancedStaffText, setEnhancedStaffText] = useState<string | null>(staffText);
  const [dataLoading, setDataLoading] = useState<boolean>(false);

  // Optional: Enhance location and staff data with real names (async)
  useEffect(() => {
    const enhanceData = async () => {
      try {
        setDataLoading(true);

        // Enhance location/facility text with real names
        if (schedule.location_id || schedule.facility_id) {
          const realLocationText = await getLocationDisplayTextAsync(
            schedule,
            getLocationData,
            getFacilityData
          );
          if (realLocationText) {
            setEnhancedLocationText(realLocationText);
          }
        }

        // Enhance staff text with real names
        if (schedule.staff_id) {
          const realStaffText = await getStaffDisplayTextAsync(schedule, getStaffData);
          if (realStaffText) {
            setEnhancedStaffText(realStaffText);
          }
        }

      } catch (error) {
        console.error('Error enhancing schedule data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    // Only enhance if we have IDs to lookup
    if (schedule.location_id || schedule.facility_id || schedule.staff_id) {
      enhanceData();
    }
  }, [schedule.id, schedule.location_id, schedule.facility_id, schedule.staff_id]);

  const handleBook = async () => {
    setBookingError(null);
    setBookingLoading(true);
    try {
      // Check if user is authenticated before booking
      if (!isAuthenticated) {
        const needsAuth = requireAuth(schedule.id, window.location.pathname);
        if (needsAuth) {
          toast.info('Redirecting to Google sign in...');
          await initiateGoogleOAuth(1); // tenantId = 1
          return;
        }
      }
      // User is authenticated, proceed with booking
      onBook?.(schedule);
    } catch (err: any) {
      setBookingError(err.message || 'Failed to initiate booking');
      toast.error('Failed to initiate booking');
    } finally {
      setBookingLoading(false);
    }
  };

  const availableSpots = schedule.pax;
  const isFullyBooked = availableSpots === 0;
  const isPrivate = schedule.is_private;
  const isButtonLoading = isAuthLoading || isOAuthLoading || bookingLoading;

  // Compact variant for the new design
  if (variant === 'compact') {
    return (
      <div className={cn(
        "bg-white/95 backdrop-blur-sm rounded-lg p-4 hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md",
        className
      )}>
        <div className="flex items-center justify-between">
          {/* Time and Duration */}
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {formatDateTime(schedule.start_time, 'time')}
              </div>
              <div className="text-xs text-gray-500">
                {formatDuration(schedule.duration)}
              </div>
            </div>

            {/* Class Info */}
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <div
                  className="h-2 w-2 rounded-full"
                  style={{ backgroundColor: sanitizeCssValue(sanitizedSchedule.calender_color) }}
                />
                <h3 className="font-medium text-gray-900">
                  {scheduleName}
                </h3>
                {scheduleDescription && (
                  <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                    {escapeHtml(scheduleDescription)}
                  </p>
                )}
                {schedule.allow_classpass && (
                  <Badge variant="outline" className="text-xs">
                    ClassPass
                  </Badge>
                )}
              </div>

              <div className="flex items-center space-x-4 text-sm text-gray-600">
                {enhancedStaffText && (
                  <span>{enhancedStaffText}</span>
                )}
                {enhancedLocationText && (
                  <span>{enhancedLocationText}</span>
                )}
              </div>
            </div>
          </div>

          {/* Book Button */}
          <Button
            onClick={handleBook}
            disabled={isFullyBooked || isButtonLoading}
            className={cn(
              "px-6 py-2 rounded-full text-sm font-medium transition-all duration-200",
              isFullyBooked
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : isAuthenticated
                ? 'bg-teal-600 text-white hover:bg-teal-700'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400'
            )}
          >
            {isFullyBooked ? 'Full' : isButtonLoading ? 'Processing...' : isAuthenticated ? 'Book Now' : (
              <div className="flex items-center space-x-1">
                <Lock className="h-4 w-4" />
                <span>Sign in</span>
              </div>
            )}
            {bookingError && (
              <div className="text-xs text-red-500 mt-1">{bookingError}</div>
            )}
          </Button>
        </div>
      </div>
    );
  }

  // Default variant (original design)
  return (
    <Card className={cn("overflow-hidden hover:shadow-lg transition-all duration-300", className)}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-3">
            {/* Status indicator */}
            <div className="flex items-center space-x-3">
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: schedule.calender_color }}
              />
              <Badge
                variant={isPrivate ? "secondary" : "default"}
                className="text-xs"
              >
                {isPrivate ? 'Private' : 'Group'}
              </Badge>
              {schedule.allow_classpass && (
                <Badge variant="outline" className="text-xs">
                  ClassPass
                </Badge>
              )}
            </div>

            {/* Class info */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {scheduleName}
                {dataLoading && (
                  <span className="ml-2 text-xs text-gray-500 animate-pulse">Loading...</span>
                )}
              </h3>
              {scheduleDescription && (
                <p className="text-sm text-gray-600 line-clamp-3">
                  {escapeHtml(scheduleDescription)}
                </p>
              )}
            </div>

            {/* Date and time */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDateTime(schedule.start_time, 'date')}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>
                  {formatDateTime(schedule.start_time, 'time')} - {formatDateTime(schedule.end_time, 'time')}
                </span>
              </div>
            </div>

            {/* Duration and capacity */}
            <div className="flex items-center space-x-4 text-sm">
              <Badge variant="outline">
                {formatDuration(schedule.duration)}
              </Badge>
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{availableSpots} spots</span>
              </div>
              {schedule.waitlist > 0 && (
                <Badge variant="secondary">
                  +{schedule.waitlist} waitlist
                </Badge>
              )}
            </div>

            {/* Location info */}
            {enhancedLocationText && (
              <div className="flex items-center space-x-1 text-sm text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>{enhancedLocationText}</span>
              </div>
            )}

            {/* Staff info */}
            {enhancedStaffText && (
              <div className="flex items-center space-x-1 text-sm text-gray-600">
                <User className="h-4 w-4" />
                <span>{enhancedStaffText}</span>
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col items-end space-y-2">
            <Button
              onClick={handleBook}
              disabled={isFullyBooked || isButtonLoading}
              className={`${isFullyBooked ? 'bg-gray-400' : 'bg-teal-600 hover:bg-teal-700'} text-white`}
            >
              {isFullyBooked ? 'Full' : isButtonLoading ? 'Processing...' : isAuthenticated ? 'Book Now' : (
                <div className="flex items-center space-x-1">
                  <Lock className="h-4 w-4" />
                  <span>Sign in to Book</span>
                </div>
              )}
              {bookingError && (
                <div className="text-xs text-red-500 mt-1">{bookingError}</div>
              )}
            </Button>
            {schedule.waitlist > 0 && isFullyBooked && (
              <Button variant="outline" size="sm">
                Join Waitlist
              </Button>
            )}
            <p className="text-xs text-gray-500">
              {availableSpots} {availableSpots === 1 ? 'spot' : 'spots'} left
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

ScheduleCard.displayName = 'ScheduleCard';



// 