import React from 'react';
import { Calendar, Search } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface ScheduleEmptyProps {
  hasFilters?: boolean;
  onClearFilters?: () => void;
  onRefresh?: () => void;
}

export function ScheduleEmpty({ hasFilters = false, onClearFilters, onRefresh }: ScheduleEmptyProps) {
  return (
    <Card className="mx-auto max-w-md">
      <CardContent className="flex flex-col items-center space-y-4 p-8 text-center">
        {hasFilters ? (
          <Search className="h-12 w-12 text-gray-400" />
        ) : (
          <Calendar className="h-12 w-12 text-gray-400" />
        )}
        
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {hasFilters ? 'No schedules found' : 'No schedules available'}
          </h3>
          <p className="text-sm text-gray-600 mt-2">
            {hasFilters 
              ? 'Try adjusting your filters to see more results.'
              : 'Check back later for new class schedules.'
            }
          </p>
        </div>

        <div className="flex space-x-2">
          {hasFilters && onClearFilters && (
            <Button variant="outline" onClick={onClearFilters}>
              Clear Filters
            </Button>
          )}
          {onRefresh && (
            <Button onClick={onRefresh}>
              Refresh
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}