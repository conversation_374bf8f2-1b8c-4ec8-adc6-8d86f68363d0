import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { format, addDays, startOfWeek, isSameDay, parseISO } from 'date-fns';

interface DateNavigationProps {
  selectedDate?: Date;
  onDateSelect: (date: Date) => void;
  className?: string;
}

export function DateNavigation({ selectedDate, onDateSelect, className }: DateNavigationProps) {
  const [currentWeekStart, setCurrentWeekStart] = useState(() => 
    startOfWeek(selectedDate || new Date(), { weekStartsOn: 1 }) // Start week on Monday
  );

  // Generate 7 days starting from current week start
  const weekDates = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i));

  const goToPreviousWeek = () => {
    setCurrentWeekStart(prev => addDays(prev, -7));
  };

  const goToNextWeek = () => {
    setCurrentWeekStart(prev => addDays(prev, 7));
  };

  const goToToday = () => {
    const today = new Date();
    setCurrentWeekStart(startOfWeek(today, { weekStartsOn: 1 }));
    onDateSelect(today);
  };

  return (
    <div className={cn("flex items-center justify-between bg-teal-800/90 p-4 rounded-lg", className)}>
      {/* Navigation Controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={goToPreviousWeek}
          className="text-white hover:bg-teal-700/50 h-8 w-8"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          onClick={goToToday}
          className="text-white hover:bg-teal-700/50 text-sm px-3 py-1 h-8"
        >
          Today
        </Button>
      </div>

      {/* Date Cards */}
      <div className="flex items-center space-x-2 flex-1 justify-center">
        {weekDates.map((date, index) => {
          const isSelected = selectedDate && isSameDay(date, selectedDate);
          const isToday = isSameDay(date, new Date());
          
          return (
            <button
              key={index}
              onClick={() => {
                console.log('📅 Date selected:', {
                  clickedDate: format(date, 'yyyy-MM-dd'),
                  dayNumber: format(date, 'd'),
                  fullDate: date.toISOString()
                });
                onDateSelect(date);
              }}
              className={cn(
                "flex flex-col items-center justify-center min-w-[60px] h-16 rounded-lg transition-all duration-200",
                "text-white hover:bg-teal-700/50",
                isSelected && "bg-white text-teal-800 shadow-md",
                isToday && !isSelected && "bg-teal-700/70"
              )}
            >
              <span className="text-xs font-medium uppercase">
                {format(date, 'EEE')}
              </span>
              <span className={cn(
                "text-lg font-bold",
                isSelected ? "text-teal-800" : "text-white"
              )}>
                {format(date, 'd')}
              </span>
            </button>
          );
        })}
      </div>

      {/* Next Week Button */}
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={goToNextWeek}
          className="text-white hover:bg-teal-700/50 h-8 w-8"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
