// ⏳ Loading States Components
// Comprehensive loading skeletons dan user feedback

'use client';

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Loader2, BookOpen, Users, Calendar } from 'lucide-react';

// Basic Loading Spinner
export const LoadingSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  message?: string;
}> = ({ size = 'md', message }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  return (
    <div className="flex items-center justify-center p-8">
      <div className="flex flex-col items-center space-y-3">
        <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-600`} />
        {message && (
          <span className="text-gray-600 text-sm">{message}</span>
        )}
      </div>
    </div>
  );
};

// Class Card Skeleton
export const ClassCardSkeleton: React.FC = () => (
  <Card className="hover:shadow-lg transition-shadow">
    <CardHeader className="pb-3">
      <div className="flex justify-between items-start gap-3">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-6 w-16" />
      </div>
    </CardHeader>
    <CardContent className="space-y-4">
      {/* Description */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-4/5" />
        <Skeleton className="h-4 w-3/5" />
      </div>
      
      {/* Details */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-28" />
          <Skeleton className="h-6 w-12 ml-auto" />
        </div>
      </div>

      {/* Actions */}
      <div className="pt-4 border-t border-gray-100">
        <Skeleton className="h-9 w-full" />
      </div>

      {/* Footer */}
      <div className="pt-2 border-t border-gray-50">
        <div className="flex justify-between">
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-3 w-24" />
        </div>
      </div>
    </CardContent>
  </Card>
);

// Classes Grid Loading
export const ClassesGridLoading: React.FC<{
  count?: number;
  message?: string;
}> = ({ count = 6, message = "Memuat data classes..." }) => (
  <div className="space-y-6">
    {/* Loading Header */}
    <div className="flex items-center justify-center p-8">
      <div className="flex items-center space-y-3 flex-col">
        <div className="flex items-center space-x-3">
          <BookOpen className="h-8 w-8 text-blue-600" />
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
        <span className="text-gray-600">{message}</span>
      </div>
    </div>
    
    {/* Loading Skeletons Grid */}
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: count }).map((_, index) => (
        <ClassCardSkeleton key={index} />
      ))}
    </div>
  </div>
);

// Page Header Loading
export const PageHeaderLoading: React.FC = () => (
  <div className="bg-white border-b border-gray-200">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-8 w-32" />
            </div>
            <Skeleton className="h-6 w-24" />
          </div>
          
          <div className="flex items-center space-x-3">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>

        {/* Search and Filters Loading */}
        <div className="mt-6 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 w-48" />
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Compact Loading for Lists
export const CompactLoading: React.FC<{
  count?: number;
}> = ({ count = 3 }) => (
  <div className="space-y-3">
    {Array.from({ length: count }).map((_, index) => (
      <Card key={index}>
        <CardContent className="p-4">
          <div className="flex justify-between items-start gap-3">
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-2/3" />
              <div className="flex gap-3">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-12" />
              <Skeleton className="h-5 w-16" />
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

// Full Page Loading
export const FullPageLoading: React.FC<{
  message?: string;
}> = ({ message = "Memuat halaman..." }) => (
  <div className="min-h-screen bg-gray-50">
    <PageHeaderLoading />
    
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <ClassesGridLoading message={message} />
    </div>

    {/* Footer Loading */}
    <footer className="bg-white border-t border-gray-200 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-2" />
            <Skeleton className="h-4 w-40" />
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
      </div>
    </footer>
  </div>
);

// Progressive Loading with Steps
export const ProgressiveLoading: React.FC<{
  steps: string[];
  currentStep: number;
}> = ({ steps, currentStep }) => (
  <div className="flex items-center justify-center p-12">
    <div className="text-center space-y-6 max-w-md">
      <div className="flex justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
      </div>
      
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-gray-900">
          Memuat Data Classes
        </h3>
        
        <div className="space-y-2">
          {steps.map((step, index) => (
            <div 
              key={index}
              className={`flex items-center space-x-3 text-sm ${
                index < currentStep 
                  ? 'text-green-600' 
                  : index === currentStep 
                  ? 'text-blue-600' 
                  : 'text-gray-400'
              }`}
            >
              <div className={`w-2 h-2 rounded-full ${
                index < currentStep 
                  ? 'bg-green-600' 
                  : index === currentStep 
                  ? 'bg-blue-600 animate-pulse' 
                  : 'bg-gray-300'
              }`} />
              <span>{step}</span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-500"
          style={{ width: `${(currentStep / steps.length) * 100}%` }}
        />
      </div>
    </div>
  </div>
);

// Loading with Retry
export const LoadingWithRetry: React.FC<{
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
}> = ({ 
  message = "Memuat data...", 
  onRetry,
  showRetry = false 
}) => (
  <div className="flex items-center justify-center p-12">
    <div className="text-center space-y-4">
      <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto" />
      <p className="text-gray-600">{message}</p>
      
      {showRetry && onRetry && (
        <button
          onClick={onRetry}
          className="text-blue-600 hover:text-blue-800 text-sm underline"
        >
          Coba lagi
        </button>
      )}
    </div>
  </div>
);
