'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { 
  AuthContextType, 
  Customer, 
  TokenPair, 
  AuthState,
  BookingContext 
} from '@/types/auth';
import { authApi } from '@/lib/api/endpoints';
import { 
  tokenStorage, 
  storeTokensSecurely, 
  clearStoredTokens, 
  hasValidSession 
} from '@/lib/utils/token-storage';
import { getDeviceContext } from '@/lib/utils/device';
import { toast } from 'sonner';

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Default tenant ID (you might want to make this configurable)
const DEFAULT_TENANT_ID = 1;

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    customer: null,
    tokens: null,
    isLoading: true,
  });

  const [bookingContext, setBookingContext] = useState<BookingContext | null>(null);

  /**
   * Update authentication state
   */
  const updateAuthState = useCallback((updates: Partial<AuthState>) => {
    setAuthState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * Initialize authentication state from stored tokens
   */
  const initializeAuth = useCallback(async () => {
    try {
      console.log('🔍 Initializing authentication state...');
      
      // Check if we have valid stored tokens
      const hasValid = await hasValidSession();
      
      if (hasValid) {
        const tokens = await tokenStorage.getTokens();
        
        if (tokens) {
          // Validate session with the server
          const validation = await authApi.validateSession();
          
          if (validation.valid && validation.customer) {
            console.log('✅ Valid session found, user is authenticated');
            
            updateAuthState({
              isAuthenticated: true,
              customer: validation.customer,
              tokens,
              isLoading: false,
            });
            
            return;
          }
        }
      }
      
      console.log('❌ No valid session found');
      updateAuthState({
        isAuthenticated: false,
        customer: null,
        tokens: null,
        isLoading: false,
      });
      
    } catch (error) {
      console.error('Auth initialization error:', error);
      
      // Clear potentially corrupted tokens
      await clearStoredTokens();
      
      updateAuthState({
        isAuthenticated: false,
        customer: null,
        tokens: null,
        isLoading: false,
      });
    }
  }, [updateAuthState]);

  /**
   * Sign in with email and password
   */
  const signIn = useCallback(async (email: string, password: string): Promise<boolean> => {
    try {
      updateAuthState({ isLoading: true });
      
      const deviceContext = getDeviceContext();
      
      const response = await authApi.signIn({
        email,
        password,
        tenantId: DEFAULT_TENANT_ID,
        deviceType: deviceContext.deviceType,
        deviceId: deviceContext.deviceId,
        rememberMe: true,
      });
      
      if (response.success) {
        // Store tokens securely
        await storeTokensSecurely(response.tokens);
        
        updateAuthState({
          isAuthenticated: true,
          customer: response.customer,
          tokens: response.tokens,
          isLoading: false,
        });
        
        toast.success('Signed in successfully!');
        
        // Handle post-auth redirect if there's a booking context
        if (bookingContext) {
          console.log('🔄 Redirecting to booking flow after authentication');
          // You can implement booking flow continuation here
          setBookingContext(null);
        }
        
        return true;
      }
      
      return false;
      
    } catch (error: any) {
      console.error('Sign in error:', error);
      
      updateAuthState({ isLoading: false });
      
      // Show user-friendly error message
      const errorMessage = error.message || 'Sign in failed. Please try again.';
      toast.error(errorMessage);
      
      return false;
    }
  }, [updateAuthState, bookingContext]);

  /**
   * Sign in with Google OAuth
   */
  const signInWithGoogle = useCallback(async (): Promise<void> => {
    try {
      updateAuthState({ isLoading: true });
      
      const deviceContext = getDeviceContext();
      
      // Initialize Google OAuth flow
      const initResponse = await authApi.initGoogleOAuth({
        tenantId: DEFAULT_TENANT_ID,
        deviceType: deviceContext.deviceType,
        deviceId: deviceContext.deviceId,
      });
      
      if (initResponse.success) {
        // Store state for validation
        sessionStorage.setItem('oauth_state', initResponse.state);
        
        // Redirect to Google OAuth
        window.location.href = initResponse.authorizationUrl;
      } else {
        throw new Error('Failed to initialize Google OAuth');
      }
      
    } catch (error: any) {
      console.error('Google sign in error:', error);
      
      updateAuthState({ isLoading: false });
      
      const errorMessage = error.message || 'Google sign in failed. Please try again.';
      toast.error(errorMessage);
    }
  }, [updateAuthState]);

  /**
   * Sign out
   */
  const signOut = useCallback(async (): Promise<void> => {
    try {
      console.log('🔍 Signing out...');
      
      // Call logout API (best effort)
      try {
        await authApi.signOut();
      } catch (error) {
        console.warn('Logout API call failed, but continuing with local cleanup:', error);
      }
      
      // Clear stored tokens
      await clearStoredTokens();
      
      // Clear auth state
      updateAuthState({
        isAuthenticated: false,
        customer: null,
        tokens: null,
        isLoading: false,
      });
      
      // Clear booking context
      setBookingContext(null);
      
      toast.success('Signed out successfully');
      
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Sign out failed. Please try again.');
    }
  }, [updateAuthState]);

  /**
   * Refresh access token
   */
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const tokens = await tokenStorage.getTokens();
      
      if (!tokens?.refreshToken) {
        console.warn('No refresh token available');
        return false;
      }
      
      const deviceContext = getDeviceContext();
      
      const response = await authApi.refreshToken({
        refreshToken: tokens.refreshToken,
        deviceType: deviceContext.deviceType,
        deviceId: deviceContext.deviceId,
      });
      
      if (response.success) {
        // Store new tokens
        await storeTokensSecurely(response.tokens);
        
        updateAuthState({
          tokens: response.tokens,
        });
        
        console.log('✅ Token refreshed successfully');
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error('Token refresh error:', error);
      
      // If refresh fails, sign out the user
      await signOut();
      
      return false;
    }
  }, [updateAuthState, signOut]);

  /**
   * Set booking context for protected flow
   */
  const setBookingContextForAuth = useCallback((context: BookingContext) => {
    setBookingContext(context);
  }, []);

  /**
   * Check if user needs authentication for booking
   */
  const requireAuthForBooking = useCallback((scheduleId: string, returnUrl?: string): boolean => {
    if (authState.isAuthenticated) {
      return false; // User is already authenticated
    }
    
    // Set booking context and require authentication
    setBookingContextForAuth({
      scheduleId,
      returnUrl,
    });
    
    return true; // User needs to authenticate
  }, [authState.isAuthenticated, setBookingContextForAuth]);

  // Initialize auth state on mount
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  // Auto-refresh tokens before they expire
  useEffect(() => {
    if (!authState.isAuthenticated || !authState.tokens) {
      return;
    }
    
    const { expiresAt } = authState.tokens;
    const now = Date.now();
    const timeUntilExpiry = expiresAt - now;
    
    // Refresh token 5 minutes before expiry
    const refreshTime = Math.max(0, timeUntilExpiry - 5 * 60 * 1000);
    
    const timeoutId = setTimeout(() => {
      refreshToken();
    }, refreshTime);
    
    return () => clearTimeout(timeoutId);
  }, [authState.tokens, authState.isAuthenticated, refreshToken]);

  const contextValue: AuthContextType = {
    isAuthenticated: authState.isAuthenticated,
    customer: authState.customer,
    tokens: authState.tokens,
    isLoading: authState.isLoading,
    signIn,
    signInWithGoogle,
    signOut,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

/**
 * Hook for protected booking flow
 */
export function useAuthForBooking() {
  const auth = useAuth();
  
  const requireAuth = useCallback((scheduleId: string, returnUrl?: string): boolean => {
    if (auth.isAuthenticated) {
      return false; // User is already authenticated
    }
    
    // Store booking context in sessionStorage for persistence
    sessionStorage.setItem('booking_context', JSON.stringify({
      scheduleId,
      returnUrl,
      timestamp: Date.now(),
    }));
    
    return true; // User needs to authenticate
  }, [auth.isAuthenticated]);
  
  const getBookingContext = useCallback((): BookingContext | null => {
    try {
      const stored = sessionStorage.getItem('booking_context');
      if (!stored) return null;
      
      const context = JSON.parse(stored);
      
      // Check if context is not too old (30 minutes)
      if (Date.now() - context.timestamp > 30 * 60 * 1000) {
        sessionStorage.removeItem('booking_context');
        return null;
      }
      
      return context;
    } catch {
      return null;
    }
  }, []);
  
  const clearBookingContext = useCallback(() => {
    sessionStorage.removeItem('booking_context');
  }, []);
  
  return {
    ...auth,
    requireAuth,
    getBookingContext,
    clearBookingContext,
  };
}
