import { NextRequest, NextResponse } from 'next/server';

/**
 * Security Headers Middleware
 * Implements comprehensive security headers to prevent XSS, clickjacking, and other attacks
 */

interface SecurityHeadersConfig {
  contentSecurityPolicy?: string;
  frameOptions?: 'DENY' | 'SAMEORIGIN' | string;
  contentTypeOptions?: boolean;
  referrerPolicy?: string;
  strictTransportSecurity?: string;
  permissionsPolicy?: string;
  crossOriginEmbedderPolicy?: string;
  crossOriginOpenerPolicy?: string;
  crossOriginResourcePolicy?: string;
}

const DEFAULT_CONFIG: Required<SecurityHeadersConfig> = {
  // Content Security Policy - Strict XSS protection
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://accounts.google.com https://oauth2.googleapis.com https://www.googleapis.com http://localhost:3000 http://localhost:3002",
    "frame-src 'self' https://accounts.google.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),
  
  // Prevent clickjacking
  frameOptions: 'DENY',
  
  // Prevent MIME type sniffing
  contentTypeOptions: true,
  
  // Control referrer information
  referrerPolicy: 'strict-origin-when-cross-origin',
  
  // HTTPS enforcement (for production)
  strictTransportSecurity: 'max-age=********; includeSubDomains; preload',
  
  // Permissions policy
  permissionsPolicy: [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'interest-cohort=()'
  ].join(', '),
  
  // Cross-origin policies
  crossOriginEmbedderPolicy: 'require-corp',
  crossOriginOpenerPolicy: 'same-origin',
  crossOriginResourcePolicy: 'same-origin',
};

/**
 * Development-specific CSP (more permissive for hot reload, etc.)
 */
const DEVELOPMENT_CSP = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com",
  "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
  "font-src 'self' https://fonts.gstatic.com",
  "img-src 'self' data: https: blob:",
  "connect-src 'self' https://accounts.google.com https://oauth2.googleapis.com https://www.googleapis.com http://localhost:* ws://localhost:*",
  "frame-src 'self' https://accounts.google.com",
  "object-src 'none'",
  "base-uri 'self'",
  "form-action 'self'"
].join('; ');

/**
 * Get environment-specific security configuration
 */
function getSecurityConfig(): SecurityHeadersConfig {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    return {
      ...DEFAULT_CONFIG,
      contentSecurityPolicy: DEVELOPMENT_CSP,
      strictTransportSecurity: '', // Don't enforce HTTPS in development
      crossOriginEmbedderPolicy: '', // Disable for development
      crossOriginOpenerPolicy: 'unsafe-none', // More permissive for development
      crossOriginResourcePolicy: 'cross-origin', // Allow cross-origin in development
    };
  }
  
  return DEFAULT_CONFIG;
}

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(
  response: NextResponse,
  config?: Partial<SecurityHeadersConfig>
): NextResponse {
  const securityConfig = { ...getSecurityConfig(), ...config };
  
  // Content Security Policy
  if (securityConfig.contentSecurityPolicy) {
    response.headers.set('Content-Security-Policy', securityConfig.contentSecurityPolicy);
  }
  
  // X-Frame-Options
  if (securityConfig.frameOptions) {
    response.headers.set('X-Frame-Options', securityConfig.frameOptions);
  }
  
  // X-Content-Type-Options
  if (securityConfig.contentTypeOptions) {
    response.headers.set('X-Content-Type-Options', 'nosniff');
  }
  
  // Referrer-Policy
  if (securityConfig.referrerPolicy) {
    response.headers.set('Referrer-Policy', securityConfig.referrerPolicy);
  }
  
  // Strict-Transport-Security (only in production with HTTPS)
  if (securityConfig.strictTransportSecurity && process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', securityConfig.strictTransportSecurity);
  }
  
  // Permissions-Policy
  if (securityConfig.permissionsPolicy) {
    response.headers.set('Permissions-Policy', securityConfig.permissionsPolicy);
  }
  
  // Cross-Origin-Embedder-Policy
  if (securityConfig.crossOriginEmbedderPolicy) {
    response.headers.set('Cross-Origin-Embedder-Policy', securityConfig.crossOriginEmbedderPolicy);
  }
  
  // Cross-Origin-Opener-Policy
  if (securityConfig.crossOriginOpenerPolicy) {
    response.headers.set('Cross-Origin-Opener-Policy', securityConfig.crossOriginOpenerPolicy);
  }
  
  // Cross-Origin-Resource-Policy
  if (securityConfig.crossOriginResourcePolicy) {
    response.headers.set('Cross-Origin-Resource-Policy', securityConfig.crossOriginResourcePolicy);
  }
  
  // Additional security headers
  response.headers.set('X-DNS-Prefetch-Control', 'off');
  response.headers.set('X-Download-Options', 'noopen');
  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none');
  
  return response;
}

/**
 * Security headers middleware
 */
export function securityHeaders(config?: Partial<SecurityHeadersConfig>) {
  return (response: NextResponse): NextResponse => {
    return applySecurityHeaders(response, config);
  };
}

/**
 * Middleware for API routes
 */
export async function withSecurityHeaders<T>(
  request: NextRequest,
  config: Partial<SecurityHeadersConfig> = {},
  handler: (request: NextRequest) => Promise<T>
): Promise<T | NextResponse> {
  try {
    const result = await handler(request);
    
    // If result is a NextResponse, apply security headers
    if (result instanceof NextResponse) {
      return applySecurityHeaders(result, config);
    }
    
    return result;
  } catch (error) {
    console.error('Handler error:', error);
    throw error;
  }
}

/**
 * XSS Protection specific headers
 */
export function xssProtectionHeaders(): Partial<SecurityHeadersConfig> {
  return {
    contentSecurityPolicy: [
      "default-src 'self'",
      "script-src 'self' 'nonce-{NONCE}' https://accounts.google.com https://apis.google.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "img-src 'self' data: https:",
      "connect-src 'self' https://accounts.google.com https://oauth2.googleapis.com http://localhost:3000",
      "frame-src 'self' https://accounts.google.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; '),
    frameOptions: 'DENY',
    contentTypeOptions: true,
  };
}

/**
 * Generate CSP nonce for inline scripts
 */
export function generateCSPNonce(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  
  // Fallback for environments without crypto.randomUUID
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Apply CSP with nonce
 */
export function applyCSPWithNonce(response: NextResponse, nonce: string): NextResponse {
  const csp = [
    "default-src 'self'",
    `script-src 'self' 'nonce-${nonce}' https://accounts.google.com https://apis.google.com`,
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://accounts.google.com https://oauth2.googleapis.com https://www.googleapis.com http://localhost:3000 http://localhost:3002",
    "frame-src 'self' https://accounts.google.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');
  
  response.headers.set('Content-Security-Policy', csp);
  return response;
}

/**
 * Validate request for potential XSS attempts
 */
export function validateRequestForXSS(request: NextRequest): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  
  // Check URL for suspicious patterns
  const url = request.url;
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /vbscript:/i,
    /onload=/i,
    /onerror=/i,
    /onclick=/i,
    /eval\(/i,
    /expression\(/i,
  ];
  
  suspiciousPatterns.forEach(pattern => {
    if (pattern.test(url)) {
      issues.push(`Suspicious pattern in URL: ${pattern.source}`);
    }
  });
  
  // Check headers for suspicious content
  const userAgent = request.headers.get('user-agent') || '';
  const referer = request.headers.get('referer') || '';
  
  suspiciousPatterns.forEach(pattern => {
    if (pattern.test(userAgent)) {
      issues.push(`Suspicious pattern in User-Agent: ${pattern.source}`);
    }
    if (pattern.test(referer)) {
      issues.push(`Suspicious pattern in Referer: ${pattern.source}`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues
  };
}
