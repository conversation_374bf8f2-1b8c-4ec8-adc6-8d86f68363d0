import { NextRequest, NextResponse } from 'next/server';
import { createHash, randomBytes } from 'crypto';

interface CSRFConfig {
  secret: string;
  cookieName?: string;
  headerName?: string;
  tokenLength?: number;
  sameSite?: 'strict' | 'lax' | 'none';
  secure?: boolean;
  httpOnly?: boolean;
  maxAge?: number;
}

const DEFAULT_CONFIG: Required<Omit<CSRFConfig, 'secret'>> = {
  cookieName: 'csrf-token',
  headerName: 'x-csrf-token',
  tokenLength: 32,
  sameSite: 'lax',
  secure: process.env.NODE_ENV === 'production',
  httpOnly: false, // Client needs to read this for AJAX requests
  maxAge: 24 * 60 * 60, // 24 hours in seconds
};

/**
 * Generate a cryptographically secure CSRF token
 */
function generateCSRFToken(secret: string, length: number = 32): string {
  const randomToken = randomBytes(length).toString('hex');
  const timestamp = Date.now().toString();
  
  // Create HMAC of random token + timestamp with secret
  const hmac = createHash('sha256')
    .update(`${randomToken}:${timestamp}:${secret}`)
    .digest('hex');
  
  // Combine random token, timestamp, and HMAC
  const token = Buffer.from(`${randomToken}:${timestamp}:${hmac}`).toString('base64');
  
  return token;
}

/**
 * Verify a CSRF token
 */
function verifyCSRFToken(token: string, secret: string, maxAge: number = 24 * 60 * 60 * 1000): boolean {
  try {
    const decoded = Buffer.from(token, 'base64').toString('utf-8');
    const [randomToken, timestamp, providedHmac] = decoded.split(':');
    
    if (!randomToken || !timestamp || !providedHmac) {
      return false;
    }
    
    // Check if token is expired
    const tokenAge = Date.now() - parseInt(timestamp);
    if (tokenAge > maxAge) {
      return false;
    }
    
    // Verify HMAC
    const expectedHmac = createHash('sha256')
      .update(`${randomToken}:${timestamp}:${secret}`)
      .digest('hex');
    
    // Use constant-time comparison to prevent timing attacks
    return constantTimeCompare(providedHmac, expectedHmac);
  } catch (error) {
    console.error('CSRF token verification error:', error);
    return false;
  }
}

/**
 * Constant-time string comparison to prevent timing attacks
 */
function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
}

/**
 * Get CSRF token from request (cookie or header)
 */
function getCSRFTokenFromRequest(request: NextRequest, config: Required<CSRFConfig>): string | null {
  // First try to get from header (for AJAX requests)
  const headerToken = request.headers.get(config.headerName);
  if (headerToken) {
    return headerToken;
  }
  
  // Then try to get from cookie
  const cookieToken = request.cookies.get(config.cookieName)?.value;
  if (cookieToken) {
    return cookieToken;
  }
  
  return null;
}

/**
 * Check if request method requires CSRF protection
 */
function requiresCSRFProtection(method: string): boolean {
  const safeMethods = ['GET', 'HEAD', 'OPTIONS'];
  return !safeMethods.includes(method.toUpperCase());
}

/**
 * CSRF protection middleware
 */
export function csrfProtection(config: CSRFConfig) {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  
  return async (request: NextRequest): Promise<NextResponse | null> => {
    const method = request.method;
    
    // Skip CSRF protection for safe methods
    if (!requiresCSRFProtection(method)) {
      return null;
    }
    
    // Get CSRF token from request
    const token = getCSRFTokenFromRequest(request, fullConfig);
    
    if (!token) {
      console.warn('CSRF protection: No token provided', {
        method,
        url: request.url,
        userAgent: request.headers.get('user-agent'),
      });
      
      return NextResponse.json(
        {
          success: false,
          error: 'CSRF token missing',
          errorCode: 'CSRF_TOKEN_MISSING',
        },
        { status: 403 }
      );
    }
    
    // Verify CSRF token
    const isValid = verifyCSRFToken(token, fullConfig.secret, fullConfig.maxAge * 1000);
    
    if (!isValid) {
      console.warn('CSRF protection: Invalid token', {
        method,
        url: request.url,
        tokenLength: token.length,
        userAgent: request.headers.get('user-agent'),
      });
      
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid CSRF token',
          errorCode: 'CSRF_TOKEN_INVALID',
        },
        { status: 403 }
      );
    }
    
    return null; // Continue to next middleware/handler
  };
}

/**
 * Generate and set CSRF token in response
 */
export function setCSRFToken(response: NextResponse, config: CSRFConfig): NextResponse {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  const token = generateCSRFToken(fullConfig.secret, fullConfig.tokenLength);
  
  // Set cookie
  response.cookies.set(fullConfig.cookieName, token, {
    httpOnly: fullConfig.httpOnly,
    secure: fullConfig.secure,
    sameSite: fullConfig.sameSite,
    maxAge: fullConfig.maxAge,
    path: '/',
  });
  
  // Also set as header for client-side access
  response.headers.set('X-CSRF-Token', token);
  
  return response;
}

/**
 * Get CSRF token for client-side use
 */
export function getCSRFTokenForClient(config: CSRFConfig): string {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  return generateCSRFToken(fullConfig.secret, fullConfig.tokenLength);
}

/**
 * Apply CSRF protection to API routes
 */
export async function withCSRFProtection<T>(
  request: NextRequest,
  config: CSRFConfig,
  handler: (request: NextRequest) => Promise<T>
): Promise<T | NextResponse> {
  const csrfResponse = await csrfProtection(config)(request);
  
  if (csrfResponse) {
    return csrfResponse;
  }
  
  try {
    const result = await handler(request);
    
    // If result is a NextResponse, set CSRF token for next request
    if (result instanceof NextResponse) {
      return setCSRFToken(result, config);
    }
    
    return result;
  } catch (error) {
    console.error('Handler error:', error);
    throw error;
  }
}

/**
 * Middleware to add CSRF token to GET requests
 */
export function addCSRFTokenToResponse(config: CSRFConfig) {
  return (response: NextResponse): NextResponse => {
    return setCSRFToken(response, config);
  };
}

/**
 * Validate CSRF token from request body (for form submissions)
 */
export async function validateCSRFFromBody(
  request: NextRequest,
  config: CSRFConfig
): Promise<boolean> {
  try {
    const body = await request.json();
    const token = body.csrfToken || body._token;
    
    if (!token) {
      return false;
    }
    
    const fullConfig = { ...DEFAULT_CONFIG, ...config };
    return verifyCSRFToken(token, fullConfig.secret, fullConfig.maxAge * 1000);
  } catch (error) {
    console.error('CSRF validation from body error:', error);
    return false;
  }
}
