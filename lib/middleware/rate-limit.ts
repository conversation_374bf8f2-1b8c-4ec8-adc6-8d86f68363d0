import { NextRequest, NextResponse } from 'next/server';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

// In-memory store (in production, use Redis or similar)
const store: RateLimitStore = {};

/**
 * Clean up expired entries from the store
 */
function cleanupStore() {
  const now = Date.now();
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key];
    }
  });
}

/**
 * Get client identifier for rate limiting
 */
function getClientId(request: NextRequest): string {
  // Try to get IP from various headers
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  let ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';
  
  // For authenticated requests, also include user agent for better uniqueness
  const userAgent = request.headers.get('user-agent') || '';
  const authHeader = request.headers.get('authorization');
  
  if (authHeader) {
    // For authenticated requests, use a hash of IP + user agent
    const identifier = `${ip}-${userAgent.substring(0, 50)}`;
    return Buffer.from(identifier).toString('base64').substring(0, 32);
  }
  
  return ip;
}

/**
 * Rate limiting middleware
 */
export function rateLimit(config: RateLimitConfig) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    // Clean up expired entries periodically
    if (Math.random() < 0.01) { // 1% chance
      cleanupStore();
    }
    
    const clientId = getClientId(request);
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    // Get or create client entry
    if (!store[clientId] || store[clientId].resetTime < now) {
      store[clientId] = {
        count: 0,
        resetTime: now + config.windowMs,
      };
    }
    
    const clientData = store[clientId];
    
    // Check if client has exceeded rate limit
    if (clientData.count >= config.maxRequests) {
      const resetTime = Math.ceil((clientData.resetTime - now) / 1000);
      
      console.warn(`Rate limit exceeded for client ${clientId.substring(0, 8)}...`, {
        count: clientData.count,
        maxRequests: config.maxRequests,
        resetTime,
      });
      
      return NextResponse.json(
        {
          success: false,
          error: config.message || 'Too many requests',
          errorCode: 'RATE_LIMIT_EXCEEDED',
          retryAfter: resetTime,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': clientData.resetTime.toString(),
            'Retry-After': resetTime.toString(),
          },
        }
      );
    }
    
    // Increment request count
    clientData.count++;
    
    // Add rate limit headers to response (will be added by the calling function)
    const remaining = Math.max(0, config.maxRequests - clientData.count);
    
    // Store rate limit info for response headers
    (request as any).rateLimitInfo = {
      limit: config.maxRequests,
      remaining,
      reset: clientData.resetTime,
    };
    
    return null; // Continue to next middleware/handler
  };
}

/**
 * Add rate limit headers to response
 */
export function addRateLimitHeaders(response: NextResponse, request: NextRequest): NextResponse {
  const rateLimitInfo = (request as any).rateLimitInfo;
  
  if (rateLimitInfo) {
    response.headers.set('X-RateLimit-Limit', rateLimitInfo.limit.toString());
    response.headers.set('X-RateLimit-Remaining', rateLimitInfo.remaining.toString());
    response.headers.set('X-RateLimit-Reset', rateLimitInfo.reset.toString());
  }
  
  return response;
}

/**
 * Predefined rate limit configurations
 */
export const rateLimitConfigs = {
  // Authentication endpoints - stricter limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 requests per 15 minutes
    message: 'Too many authentication attempts. Please try again later.',
  },
  
  // OAuth endpoints - moderate limits
  oauth: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 5, // 5 requests per 5 minutes
    message: 'Too many OAuth requests. Please try again later.',
  },
  
  // API endpoints - generous limits
  api: {
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
    message: 'Too many API requests. Please slow down.',
  },
  
  // Public endpoints - very generous limits
  public: {
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 200, // 200 requests per minute
    message: 'Too many requests. Please slow down.',
  },
};

/**
 * Apply rate limiting to API routes
 */
export async function withRateLimit<T>(
  request: NextRequest,
  config: RateLimitConfig,
  handler: (request: NextRequest) => Promise<T>
): Promise<T | NextResponse> {
  const rateLimitResponse = await rateLimit(config)(request);
  
  if (rateLimitResponse) {
    return rateLimitResponse;
  }
  
  try {
    const result = await handler(request);
    
    // If result is a NextResponse, add rate limit headers
    if (result instanceof NextResponse) {
      return addRateLimitHeaders(result, request);
    }
    
    return result;
  } catch (error) {
    console.error('Handler error:', error);
    throw error;
  }
}

/**
 * Get rate limit status for a client
 */
export function getRateLimitStatus(request: NextRequest, config: RateLimitConfig) {
  const clientId = getClientId(request);
  const clientData = store[clientId];
  const now = Date.now();

  if (!clientData || clientData.resetTime < now) {
    return {
      limit: config.maxRequests,
      remaining: config.maxRequests,
      reset: now + config.windowMs,
      resetTime: Math.ceil(config.windowMs / 1000),
    };
  }

  return {
    limit: config.maxRequests,
    remaining: Math.max(0, config.maxRequests - clientData.count),
    reset: clientData.resetTime,
    resetTime: Math.ceil((clientData.resetTime - now) / 1000),
  };
}
