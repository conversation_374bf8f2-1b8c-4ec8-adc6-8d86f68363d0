import { ClassSchedule } from '@/types/schedule';
import { format, parseISO, isSameDay } from 'date-fns';

export interface GroupedSchedules {
  [date: string]: ClassSchedule[];
}

/**
 * Groups schedules by their start date
 */
export function groupSchedulesByDate(schedules: ClassSchedule[]): GroupedSchedules {
  const grouped: GroupedSchedules = {};
  
  schedules.forEach(schedule => {
    try {
      const date = parseISO(schedule.start_time);
      const dateKey = format(date, 'yyyy-MM-dd');
      
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      
      grouped[dateKey].push(schedule);
    } catch (error) {
      console.error('Error parsing schedule date:', error, schedule);
    }
  });
  
  // Sort schedules within each date by start time
  Object.keys(grouped).forEach(dateKey => {
    grouped[dateKey].sort((a, b) => {
      try {
        const timeA = parseISO(a.start_time);
        const timeB = parseISO(b.start_time);
        return timeA.getTime() - timeB.getTime();
      } catch (error) {
        return 0;
      }
    });
  });
  
  return grouped;
}

/**
 * Gets schedules for a specific date
 */
export function getSchedulesForDate(schedules: ClassSchedule[], targetDate: Date): ClassSchedule[] {
  console.log('🔍 Filtering schedules for date:', {
    targetDate: format(targetDate, 'yyyy-MM-dd'),
    totalSchedules: schedules.length
  });

  const filtered = schedules.filter(schedule => {
    try {
      const scheduleDate = parseISO(schedule.start_time);
      const isSame = isSameDay(scheduleDate, targetDate);

      console.log('📅 Schedule date comparison:', {
        scheduleId: schedule.id.slice(-8),
        scheduleStartTime: schedule.start_time,
        scheduleDateFormatted: format(scheduleDate, 'yyyy-MM-dd'),
        targetDateFormatted: format(targetDate, 'yyyy-MM-dd'),
        isSameDay: isSame
      });

      return isSame;
    } catch (error) {
      console.error('❌ Date parsing error:', error, schedule);
      return false;
    }
  });

  console.log('✅ Filtered result:', {
    filteredCount: filtered.length,
    scheduleIds: filtered.map(s => s.id.slice(-8))
  });

  return filtered;
}

/**
 * Gets unique dates from schedules
 */
export function getUniqueDatesFromSchedules(schedules: ClassSchedule[]): string[] {
  const dates = new Set<string>();
  
  schedules.forEach(schedule => {
    try {
      const date = parseISO(schedule.start_time);
      const dateKey = format(date, 'yyyy-MM-dd');
      dates.add(dateKey);
    } catch (error) {
      console.error('Error parsing schedule date:', error);
    }
  });
  
  return Array.from(dates).sort();
}

/**
 * Filters schedules by selected date range
 */
export function filterSchedulesByDateRange(
  schedules: ClassSchedule[],
  startDate?: Date,
  endDate?: Date
): ClassSchedule[] {
  if (!startDate && !endDate) {
    return schedules;
  }

  return schedules.filter(schedule => {
    try {
      const scheduleDate = parseISO(schedule.start_time);

      if (startDate && scheduleDate < startDate) {
        return false;
      }

      if (endDate && scheduleDate > endDate) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  });
}

/**
 * Gets a display name for a schedule based on available data
 * This is a synchronous fallback - use getScheduleDisplayNameAsync for real data
 */
export function getScheduleDisplayName(schedule: ClassSchedule): string {
  const type = schedule.is_private ? 'Private Session' : 'Group Class';
  const classId = schedule.class_id ? schedule.class_id.slice(-8) : 'Unknown';
  return `${type} - ${classId}`;
}

/**
 * Gets a display name for a schedule using real class data (async)
 */
export async function getScheduleDisplayNameAsync(
  schedule: ClassSchedule,
  getClassData: (id: string) => Promise<any>
): Promise<string> {
  const type = schedule.is_private ? 'Private Session' : 'Group Class';

  try {
    const classData = await getClassData(schedule.class_id);
    if (classData && classData.name) {
      return `${classData.name} (${type})`;
    }
  } catch (error) {
    console.error('Error fetching class name:', error);
  }

  // Fallback to ID-based name
  const classId = schedule.class_id ? schedule.class_id.slice(-8) : 'Unknown';
  return `${type} - ${classId}`;
}

/**
 * Gets location display text from schedule (synchronous fallback)
 */
export function getLocationDisplayText(schedule: ClassSchedule): string | null {
  const parts: string[] = [];

  if (schedule.location_id) {
    parts.push(`Location: ${schedule.location_id.slice(-8)}`);
  }

  if (schedule.facility_id) {
    parts.push(`Facility: ${schedule.facility_id.slice(-8)}`);
  }

  return parts.length > 0 ? parts.join(' • ') : null;
}

/**
 * Gets location display text using real data (async)
 */
export async function getLocationDisplayTextAsync(
  schedule: ClassSchedule,
  getLocationData: (id: string) => Promise<any>,
  getFacilityData: (id: string) => Promise<any>
): Promise<string | null> {
  const parts: string[] = [];

  try {
    if (schedule.location_id) {
      const locationData = await getLocationData(schedule.location_id);
      if (locationData && locationData.name) {
        parts.push(locationData.name);
      }
    }

    if (schedule.facility_id) {
      const facilityData = await getFacilityData(schedule.facility_id);
      if (facilityData && facilityData.name) {
        parts.push(facilityData.name);
      }
    }
  } catch (error) {
    console.error('Error fetching location/facility data:', error);
  }

  return parts.length > 0 ? parts.join(' • ') : null;
}

/**
 * Gets staff display text from schedule (synchronous fallback)
 */
export function getStaffDisplayText(schedule: ClassSchedule): string | null {
  return schedule.staff_id ? `Staff: ${schedule.staff_id.slice(-8)}` : null;
}

/**
 * Gets staff display text using real data (async)
 */
export async function getStaffDisplayTextAsync(
  schedule: ClassSchedule,
  getStaffData: (id: string) => Promise<any>
): Promise<string | null> {
  if (!schedule.staff_id) return null;

  try {
    const staffData = await getStaffData(schedule.staff_id);
    if (staffData && staffData.name) {
      return `Instructor: ${staffData.name}`;
    }
  } catch (error) {
    console.error('Error fetching staff data:', error);
  }

  // Fallback to ID-based name
  return `Staff: ${schedule.staff_id.slice(-8)}`;
}
