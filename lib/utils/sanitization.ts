'use client';

import DOMPurify from 'dompurify';

/**
 * XSS Protection Utilities
 * Provides comprehensive sanitization for user inputs and API responses
 */

// Configuration for different sanitization levels
const SANITIZATION_CONFIGS = {
  // Strict: Only allow basic text formatting
  strict: {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_DOM_IMPORT: false,
  },
  
  // Basic: Allow common safe HTML tags
  basic: {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'span'],
    ALLOWED_ATTR: ['class'],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_DOM_IMPORT: false,
  },
  
  // Text only: Strip all HTML tags
  textOnly: {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_DOM_IMPORT: false,
  },
  
  // None: For display purposes only, no sanitization
  none: null,
};

/**
 * Sanitize user input to prevent XSS attacks
 */
export function sanitizeUserInput(
  input: string | null | undefined,
  level: 'strict' | 'basic' | 'textOnly' = 'strict'
): string {
  if (!input || typeof input !== 'string') {
    return '';
  }
  
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    // Server-side: Basic HTML entity encoding
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
  
  const config = SANITIZATION_CONFIGS[level];
  if (!config) {
    return input;
  }
  
  try {
    return DOMPurify.sanitize(input, config);
  } catch (error) {
    console.error('Sanitization error:', error);
    // Fallback to basic HTML entity encoding
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }
}

/**
 * Sanitize API response data
 */
export function sanitizeApiResponse<T extends Record<string, any>>(
  data: T,
  fieldsToSanitize: (keyof T)[] = []
): T {
  if (!data || typeof data !== 'object') {
    return data;
  }
  
  const sanitized = { ...data };
  
  // Default fields that should always be sanitized
  const defaultFields = ['name', 'title', 'description', 'email', 'firstName', 'lastName', 'displayName'];
  const allFields = [...new Set([...defaultFields, ...fieldsToSanitize])];
  
  allFields.forEach(field => {
    if (field in sanitized && typeof sanitized[field] === 'string') {
      sanitized[field] = sanitizeUserInput(sanitized[field], 'textOnly') as T[keyof T];
    }
  });
  
  return sanitized;
}

/**
 * Sanitize customer data specifically
 */
export function sanitizeCustomerData(customer: any) {
  if (!customer) return customer;
  
  return sanitizeApiResponse(customer, [
    'firstName',
    'lastName', 
    'displayName',
    'email',
    'membershipType',
    'image' // URL sanitization
  ]);
}

/**
 * Sanitize schedule data
 */
export function sanitizeScheduleData(schedule: any) {
  if (!schedule) return schedule;
  
  return sanitizeApiResponse(schedule, [
    'class_name',
    'instructor_name',
    'location_name',
    'facility_name',
    'description',
    'notes'
  ]);
}

/**
 * Validate and sanitize URL to prevent javascript: and data: URLs
 */
export function sanitizeUrl(url: string | null | undefined): string {
  if (!url || typeof url !== 'string') {
    return '';
  }
  
  // Remove dangerous protocols
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'about:'];
  const lowerUrl = url.toLowerCase().trim();
  
  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      console.warn('Blocked dangerous URL:', url);
      return '';
    }
  }
  
  // Allow only safe protocols
  const safeProtocols = ['http:', 'https:', 'mailto:', 'tel:', 'ftp:'];
  const hasProtocol = lowerUrl.includes('://');
  
  if (hasProtocol) {
    const isValidProtocol = safeProtocols.some(protocol => lowerUrl.startsWith(protocol));
    if (!isValidProtocol) {
      console.warn('Blocked invalid protocol URL:', url);
      return '';
    }
  }
  
  // Basic URL sanitization
  return url.replace(/[<>"']/g, '');
}

/**
 * Sanitize CSS values to prevent CSS injection
 */
export function sanitizeCssValue(value: string | null | undefined): string {
  if (!value || typeof value !== 'string') {
    return '';
  }
  
  // Remove dangerous CSS functions and properties
  const dangerousPatterns = [
    /expression\s*\(/gi,
    /javascript\s*:/gi,
    /vbscript\s*:/gi,
    /data\s*:/gi,
    /import\s*['"]/gi,
    /@import/gi,
    /url\s*\(\s*['"]?\s*javascript\s*:/gi,
  ];
  
  let sanitized = value;
  dangerousPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  return sanitized;
}

/**
 * Escape HTML entities for safe display
 */
export function escapeHtml(text: string | null | undefined): string {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  const entityMap: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;',
    '`': '&#x60;',
    '=': '&#x3D;'
  };
  
  return text.replace(/[&<>"'`=\/]/g, (char) => entityMap[char] || char);
}

/**
 * Validate input length to prevent DoS attacks
 */
export function validateInputLength(
  input: string | null | undefined,
  maxLength: number = 1000
): boolean {
  if (!input || typeof input !== 'string') {
    return true;
  }
  
  if (input.length > maxLength) {
    console.warn(`Input too long: ${input.length} > ${maxLength}`);
    return false;
  }
  
  return true;
}

/**
 * Comprehensive input validation and sanitization
 */
export function validateAndSanitizeInput(
  input: string | null | undefined,
  options: {
    maxLength?: number;
    sanitizationLevel?: 'strict' | 'basic' | 'textOnly';
    allowEmpty?: boolean;
  } = {}
): { isValid: boolean; sanitized: string; error?: string } {
  const {
    maxLength = 1000,
    sanitizationLevel = 'strict',
    allowEmpty = true
  } = options;
  
  // Check if input exists
  if (!input || typeof input !== 'string') {
    if (allowEmpty) {
      return { isValid: true, sanitized: '' };
    }
    return { isValid: false, sanitized: '', error: 'Input is required' };
  }
  
  // Validate length
  if (!validateInputLength(input, maxLength)) {
    return { 
      isValid: false, 
      sanitized: '', 
      error: `Input too long (max ${maxLength} characters)` 
    };
  }
  
  // Sanitize input
  const sanitized = sanitizeUserInput(input, sanitizationLevel);
  
  return { isValid: true, sanitized };
}

/**
 * Safe JSON parsing with XSS protection
 */
export function safeJsonParse<T = any>(
  jsonString: string | null | undefined,
  fallback: T
): T {
  if (!jsonString || typeof jsonString !== 'string') {
    return fallback;
  }
  
  try {
    const parsed = JSON.parse(jsonString);
    
    // If parsed result is an object, sanitize string values
    if (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) {
      return sanitizeApiResponse(parsed) as T;
    }
    
    return parsed;
  } catch (error) {
    console.error('JSON parsing error:', error);
    return fallback;
  }
}
