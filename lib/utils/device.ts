import { DeviceContext } from '@/types/auth';

/**
 * Generate a unique device ID for the current browser/device
 */
export function generateDeviceId(): string {
  // Try to get existing device ID from localStorage
  const existingId = localStorage.getItem('device_id');
  if (existingId) {
    return existingId;
  }

  // Generate new device ID based on browser characteristics
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx!.textBaseline = 'top';
  ctx!.font = '14px Arial';
  ctx!.fillText('Device fingerprint', 2, 2);
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL(),
    navigator.hardwareConcurrency || 0,
    navigator.maxTouchPoints || 0,
  ].join('|');

  // Create hash of fingerprint
  const deviceId = btoa(fingerprint)
    .replace(/[^a-zA-Z0-9]/g, '')
    .substring(0, 32);

  // Store for future use
  localStorage.setItem('device_id', deviceId);
  
  return deviceId;
}

/**
 * Get current device context
 */
export function getDeviceContext(): DeviceContext {
  return {
    deviceType: getDeviceType(),
    deviceId: generateDeviceId(),
    userAgent: navigator.userAgent,
  };
}

/**
 * Detect device type based on user agent and screen size
 */
export function getDeviceType(): 'web' | 'mobile' | 'tablet' {
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
  
  if (isMobile) {
    // Distinguish between mobile and tablet
    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || 
                     (screen.width >= 768 && screen.height >= 1024);
    return isTablet ? 'tablet' : 'mobile';
  }
  
  return 'web';
}

/**
 * Get client IP address (best effort)
 */
export async function getClientIP(): Promise<string | null> {
  try {
    // Try to get IP from WebRTC (works in most browsers)
    const pc = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    });
    
    return new Promise((resolve) => {
      pc.createDataChannel('');
      pc.createOffer().then(offer => pc.setLocalDescription(offer));
      
      pc.onicecandidate = (ice) => {
        if (!ice || !ice.candidate || !ice.candidate.candidate) return;
        
        const candidate = ice.candidate.candidate;
        const ipMatch = candidate.match(/([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/);
        
        if (ipMatch) {
          pc.close();
          resolve(ipMatch[1]);
        }
      };
      
      // Timeout after 3 seconds
      setTimeout(() => {
        pc.close();
        resolve(null);
      }, 3000);
    });
  } catch (error) {
    console.warn('Could not determine client IP:', error);
    return null;
  }
}

/**
 * Generate secure random state for OAuth
 */
export function generateSecureState(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Generate PKCE code verifier
 */
export function generateCodeVerifier(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return btoa(String.fromCharCode.apply(null, Array.from(array)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Generate PKCE code challenge from verifier
 */
export async function generateCodeChallenge(verifier: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(verifier);
  const digest = await crypto.subtle.digest('SHA-256', data);
  
  return btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(digest))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Check if browser supports secure features
 */
export function checkBrowserSecurity(): {
  isSecure: boolean;
  hasLocalStorage: boolean;
  hasSessionStorage: boolean;
  hasCrypto: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  
  const isSecure = window.location.protocol === 'https:' || 
                   window.location.hostname === 'localhost';
  if (!isSecure) {
    issues.push('Not running on HTTPS');
  }
  
  const hasLocalStorage = typeof Storage !== 'undefined' && !!localStorage;
  if (!hasLocalStorage) {
    issues.push('localStorage not available');
  }
  
  const hasSessionStorage = typeof Storage !== 'undefined' && !!sessionStorage;
  if (!hasSessionStorage) {
    issues.push('sessionStorage not available');
  }
  
  const hasCrypto = !!window.crypto && !!window.crypto.subtle;
  if (!hasCrypto) {
    issues.push('Web Crypto API not available');
  }
  
  return {
    isSecure,
    hasLocalStorage,
    hasSessionStorage,
    hasCrypto,
    issues
  };
}

/**
 * Sanitize device information for logging
 */
export function sanitizeDeviceInfo(deviceContext: DeviceContext): Partial<DeviceContext> {
  return {
    deviceType: deviceContext.deviceType,
    deviceId: deviceContext.deviceId?.substring(0, 8) + '...', // Only first 8 chars
    userAgent: deviceContext.userAgent?.substring(0, 100), // Truncate long user agents
  };
}

/**
 * Detect if user is likely using a VPN or proxy
 */
export function detectVPN(): boolean {
  // Basic VPN detection based on common patterns
  const userAgent = navigator.userAgent.toLowerCase();
  const vpnIndicators = [
    'vpn',
    'proxy',
    'tor',
    'anonymizer',
    'hide',
    'mask'
  ];
  
  return vpnIndicators.some(indicator => userAgent.includes(indicator));
}

/**
 * Get browser fingerprint for additional security
 */
export function getBrowserFingerprint(): string {
  const fingerprint = {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
    hardwareConcurrency: navigator.hardwareConcurrency,
    maxTouchPoints: navigator.maxTouchPoints,
  };
  
  return btoa(JSON.stringify(fingerprint))
    .replace(/[^a-zA-Z0-9]/g, '')
    .substring(0, 64);
}

/**
 * Validate device context for security
 */
export function validateDeviceContext(context: DeviceContext): {
  valid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  
  if (!context.deviceId || context.deviceId.length < 16) {
    issues.push('Invalid device ID');
  }
  
  if (!context.deviceType || !['web', 'mobile', 'tablet'].includes(context.deviceType)) {
    issues.push('Invalid device type');
  }
  
  if (!context.userAgent || context.userAgent.length < 10) {
    issues.push('Invalid user agent');
  }
  
  return {
    valid: issues.length === 0,
    issues
  };
}
