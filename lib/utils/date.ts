import { format, parseISO, isValid } from 'date-fns';

export function formatDateTime(dateString: string, type: 'date' | 'time' | 'datetime' = 'datetime'): string {
  try {
    const date = parseISO(dateString);
    
    if (!isValid(date)) {
      return 'Invalid date';
    }

    switch (type) {
      case 'date':
        return format(date, 'MMM dd, yyyy');
      case 'time':
        return format(date, 'h:mm a');
      case 'datetime':
        return format(date, 'MMM dd, yyyy HH:mm');
      default:
        return format(date, 'MMM dd, yyyy HH:mm');
    }
  } catch (error) {
    console.error('Date formatting error:', error);
    return 'Invalid date';
  }
}

export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}min`;
}

export function isDateInRange(date: string, startDate?: string, endDate?: string): boolean {
  try {
    const targetDate = parseISO(date);
    
    if (startDate) {
      const start = parseISO(startDate);
      if (targetDate < start) return false;
    }
    
    if (endDate) {
      const end = parseISO(endDate);
      if (targetDate > end) return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
}