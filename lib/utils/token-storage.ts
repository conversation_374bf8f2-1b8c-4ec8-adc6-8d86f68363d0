import { TokenPair, TokenStorage } from '@/types/auth';

/**
 * Secure token storage implementation for web browsers
 * Uses multiple storage mechanisms for redundancy and security
 */
class SecureTokenStorage implements TokenStorage {
  private readonly ACCESS_TOKEN_KEY = 'auth_access_token';
  private readonly REFRESH_TOKEN_KEY = 'auth_refresh_token';
  private readonly EXPIRES_AT_KEY = 'auth_expires_at';
  private readonly CSRF_TOKEN_KEY = 'auth_csrf_token';
  
  /**
   * Encrypt data using Web Crypto API
   */
  private async encrypt(data: string): Promise<string> {
    try {
      if (!window.crypto || !window.crypto.subtle) {
        // Fallback to base64 encoding if crypto not available
        return btoa(data);
      }

      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      
      // Generate a random key
      const key = await window.crypto.subtle.generateKey(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );
      
      // Generate random IV
      const iv = window.crypto.getRandomValues(new Uint8Array(12));
      
      // Encrypt the data
      const encrypted = await window.crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        key,
        dataBuffer
      );
      
      // Export the key
      const exportedKey = await window.crypto.subtle.exportKey('raw', key);
      
      // Combine key, IV, and encrypted data
      const combined = new Uint8Array(
        exportedKey.byteLength + iv.byteLength + encrypted.byteLength
      );
      combined.set(new Uint8Array(exportedKey), 0);
      combined.set(iv, exportedKey.byteLength);
      combined.set(new Uint8Array(encrypted), exportedKey.byteLength + iv.byteLength);
      
      return btoa(String.fromCharCode.apply(null, Array.from(combined)));
    } catch (error) {
      console.warn('Encryption failed, using base64:', error);
      return btoa(data);
    }
  }
  
  /**
   * Decrypt data using Web Crypto API
   */
  private async decrypt(encryptedData: string): Promise<string> {
    try {
      if (!window.crypto || !window.crypto.subtle) {
        // Fallback to base64 decoding
        return atob(encryptedData);
      }

      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );
      
      // Extract key, IV, and encrypted data
      const keyData = combined.slice(0, 32);
      const iv = combined.slice(32, 44);
      const encrypted = combined.slice(44);
      
      // Import the key
      const key = await window.crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );
      
      // Decrypt the data
      const decrypted = await window.crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        key,
        encrypted
      );
      
      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      console.warn('Decryption failed, using base64:', error);
      return atob(encryptedData);
    }
  }
  
  /**
   * Store data securely in localStorage with encryption
   */
  private async setSecureItem(key: string, value: string): Promise<void> {
    try {
      const encrypted = await this.encrypt(value);
      localStorage.setItem(key, encrypted);
      
      // Also store in sessionStorage as backup
      sessionStorage.setItem(`${key}_backup`, encrypted);
    } catch (error) {
      console.error('Failed to store secure item:', error);
      throw new Error('Token storage failed');
    }
  }
  
  /**
   * Retrieve and decrypt data from localStorage
   */
  private async getSecureItem(key: string): Promise<string | null> {
    try {
      let encrypted = localStorage.getItem(key);
      
      // Fallback to sessionStorage if localStorage fails
      if (!encrypted) {
        encrypted = sessionStorage.getItem(`${key}_backup`);
      }
      
      if (!encrypted) {
        return null;
      }
      
      return await this.decrypt(encrypted);
    } catch (error) {
      console.error('Failed to retrieve secure item:', error);
      return null;
    }
  }
  
  /**
   * Remove item from all storage locations
   */
  private removeSecureItem(key: string): void {
    localStorage.removeItem(key);
    sessionStorage.removeItem(`${key}_backup`);
  }
  
  /**
   * Get stored tokens
   */
  async getTokens(): Promise<TokenPair | null> {
    try {
      const accessToken = await this.getSecureItem(this.ACCESS_TOKEN_KEY);
      const refreshToken = await this.getSecureItem(this.REFRESH_TOKEN_KEY);
      const expiresAtStr = await this.getSecureItem(this.EXPIRES_AT_KEY);
      
      if (!accessToken || !refreshToken || !expiresAtStr) {
        return null;
      }
      
      const expiresAt = parseInt(expiresAtStr, 10);
      const expiresIn = Math.max(0, Math.floor((expiresAt - Date.now()) / 1000));
      
      return {
        accessToken,
        refreshToken,
        tokenType: 'Bearer',
        expiresIn,
        expiresAt,
      };
    } catch (error) {
      console.error('Failed to get tokens:', error);
      return null;
    }
  }
  
  /**
   * Store tokens securely
   */
  async setTokens(tokens: TokenPair): Promise<void> {
    try {
      await this.setSecureItem(this.ACCESS_TOKEN_KEY, tokens.accessToken);
      await this.setSecureItem(this.REFRESH_TOKEN_KEY, tokens.refreshToken);
      await this.setSecureItem(this.EXPIRES_AT_KEY, tokens.expiresAt.toString());
      
      console.log('✅ Tokens stored securely');
    } catch (error) {
      console.error('❌ Failed to store tokens:', error);
      throw error;
    }
  }
  
  /**
   * Clear all stored tokens
   */
  async clearTokens(): Promise<void> {
    try {
      this.removeSecureItem(this.ACCESS_TOKEN_KEY);
      this.removeSecureItem(this.REFRESH_TOKEN_KEY);
      this.removeSecureItem(this.EXPIRES_AT_KEY);
      this.removeSecureItem(this.CSRF_TOKEN_KEY);
      
      console.log('🗑️ Tokens cleared');
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  }
  
  /**
   * Get CSRF token
   */
  async getCSRFToken(): Promise<string | null> {
    return await this.getSecureItem(this.CSRF_TOKEN_KEY);
  }
  
  /**
   * Set CSRF token
   */
  async setCSRFToken(token: string): Promise<void> {
    await this.setSecureItem(this.CSRF_TOKEN_KEY, token);
  }
  
  /**
   * Check if tokens exist and are not expired
   */
  async hasValidTokens(): Promise<boolean> {
    const tokens = await this.getTokens();
    if (!tokens) return false;
    
    // Check if token expires in next 5 minutes
    const expiresIn = tokens.expiresAt - Date.now();
    return expiresIn > 5 * 60 * 1000;
  }
  
  /**
   * Get access token if valid
   */
  async getValidAccessToken(): Promise<string | null> {
    const tokens = await this.getTokens();
    if (!tokens) return null;
    
    // Check if token is still valid (with 1 minute buffer)
    const expiresIn = tokens.expiresAt - Date.now();
    if (expiresIn <= 60 * 1000) {
      return null;
    }
    
    return tokens.accessToken;
  }
}

// Singleton instance
export const tokenStorage = new SecureTokenStorage();

/**
 * Utility functions for token management
 */

/**
 * Store tokens securely
 */
export async function storeTokensSecurely(tokens: TokenPair): Promise<void> {
  return tokenStorage.setTokens(tokens);
}

/**
 * Get stored tokens
 */
export async function getStoredTokens(): Promise<TokenPair | null> {
  return tokenStorage.getTokens();
}

/**
 * Clear stored tokens
 */
export async function clearStoredTokens(): Promise<void> {
  return tokenStorage.clearTokens();
}

/**
 * Get valid access token
 */
export async function getAccessToken(): Promise<string | null> {
  return tokenStorage.getValidAccessToken();
}

/**
 * Check if user has valid session
 */
export async function hasValidSession(): Promise<boolean> {
  return tokenStorage.hasValidTokens();
}

/**
 * Validate stored token format
 */
export async function validateStoredToken(token: string): Promise<boolean> {
  try {
    // Basic JWT format validation
    const parts = token.split('.');
    if (parts.length !== 3) return false;
    
    // Try to decode payload
    const payload = JSON.parse(atob(parts[1]));
    
    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Get CSRF token for requests
 */
export async function getCSRFToken(): Promise<string> {
  let csrfToken = await tokenStorage.getCSRFToken();
  
  if (!csrfToken) {
    // Generate new CSRF token
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    csrfToken = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    
    await tokenStorage.setCSRFToken(csrfToken);
  }
  
  return csrfToken;
}
