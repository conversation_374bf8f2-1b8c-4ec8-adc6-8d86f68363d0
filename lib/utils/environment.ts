/**
 * Environment and Port Detection Utilities
 * Handles dynamic port detection for development and production environments
 */

/**
 * Get the current application URL with proper port detection
 */
export function getAppUrl(): string {
  // In browser environment
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  // In server environment
  const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
  const host = process.env.VERCEL_URL || process.env.NEXT_PUBLIC_VERCEL_URL;
  
  if (host) {
    return `${protocol}://${host}`;
  }
  
  // Development fallback - try to detect port from environment
  const port = process.env.PORT || process.env.NEXT_PUBLIC_PORT || '3000';
  const hostname = process.env.HOSTNAME || 'localhost';
  
  return `${protocol}://${hostname}:${port}`;
}

/**
 * Get Google OAuth redirect URI with dynamic port detection
 */
export function getGoogleRedirectUri(): string {
  // Use environment variable if explicitly set
  if (process.env.GOOGLE_REDIRECT_URI) {
    return process.env.GOOGLE_REDIRECT_URI;
  }
  
  // Dynamic detection for development
  const appUrl = getAppUrl();
  return `${appUrl}/auth/callback`;
}

/**
 * Get API base URL with proper port detection
 */
export function getApiBaseUrl(): string {
  // Use environment variable if explicitly set
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL;
  }
  
  // Dynamic detection
  return getAppUrl();
}

/**
 * Get external API base URL (backend service)
 */
export function getExternalApiBaseUrl(): string {
  return process.env.EXTERNAL_API_BASE_URL || 'http://localhost:3000';
}

/**
 * Check if we're in development environment
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Check if we're in production environment
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Get current port number
 */
export function getCurrentPort(): number {
  if (typeof window !== 'undefined') {
    return parseInt(window.location.port) || (window.location.protocol === 'https:' ? 443 : 80);
  }
  
  return parseInt(process.env.PORT || process.env.NEXT_PUBLIC_PORT || '3003');
}

/**
 * Get all possible OAuth redirect URIs for development
 * Useful for Google Cloud Console configuration
 */
export function getAllPossibleRedirectUris(): string[] {
  const baseUris = [
    'http://localhost:3000/auth/callback',
    'http://localhost:3001/auth/callback', 
    'http://localhost:3002/auth/callback',
    'http://localhost:3003/auth/callback',
    'http://localhost:3004/auth/callback',
  ];
  
  // Add production URI if available
  if (process.env.VERCEL_URL) {
    baseUris.push(`https://${process.env.VERCEL_URL}/auth/callback`);
  }
  
  return baseUris;
}

/**
 * Validate current environment configuration
 */
export function validateEnvironmentConfig(): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check Google OAuth configuration
  if (!process.env.GOOGLE_CLIENT_ID) {
    issues.push('GOOGLE_CLIENT_ID is not set');
  }
  
  if (!process.env.GOOGLE_CLIENT_SECRET) {
    issues.push('GOOGLE_CLIENT_SECRET is not set');
  }
  
  // Check if redirect URI matches current port
  const currentAppUrl = getAppUrl();
  const configuredRedirectUri = process.env.GOOGLE_REDIRECT_URI;
  
  if (configuredRedirectUri && !configuredRedirectUri.includes(currentAppUrl)) {
    issues.push(`Google redirect URI (${configuredRedirectUri}) doesn't match current app URL (${currentAppUrl})`);
    recommendations.push(`Update GOOGLE_REDIRECT_URI to: ${currentAppUrl}/auth/callback`);
  }
  
  // Check external API configuration
  if (!process.env.EXTERNAL_API_BASE_URL) {
    recommendations.push('Set EXTERNAL_API_BASE_URL for backend API communication');
  }
  
  if (!process.env.EXTERNAL_API_KEY) {
    issues.push('EXTERNAL_API_KEY is not set');
  }
  
  // Security checks
  if (isDevelopment()) {
    if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
      recommendations.push('Set a strong JWT_SECRET (minimum 32 characters)');
    }
    
    if (!process.env.CSRF_SECRET) {
      recommendations.push('Set CSRF_SECRET for CSRF protection');
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    recommendations
  };
}

/**
 * Log environment configuration for debugging
 */
export function logEnvironmentConfig(): void {
  if (!isDevelopment()) {
    return; // Don't log in production
  }
  
  console.log('🔧 Environment Configuration:');
  console.log('  App URL:', getAppUrl());
  console.log('  API Base URL:', getApiBaseUrl());
  console.log('  External API URL:', getExternalApiBaseUrl());
  console.log('  Google Redirect URI:', getGoogleRedirectUri());
  console.log('  Current Port:', getCurrentPort());
  console.log('  Environment:', process.env.NODE_ENV);
  
  const validation = validateEnvironmentConfig();
  if (!validation.isValid) {
    console.warn('⚠️ Environment Issues:', validation.issues);
  }
  
  if (validation.recommendations.length > 0) {
    console.info('💡 Recommendations:', validation.recommendations);
  }
  
  console.log('🔗 All Possible Redirect URIs for Google Cloud Console:');
  getAllPossibleRedirectUris().forEach(uri => console.log('  -', uri));
}
