import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date)
}

export function formatTime(time: string): string {
  return new Intl.DateTimeFormat("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  }).format(new Date(`2000-01-01T${time}`))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount)
}

export function getRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return "just now"
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`

  return formatDate(date)
}

export function generateTimeSlots(startTime: string, endTime: string, duration: number, interval = 15): string[] {
  const slots: string[] = []
  const start = new Date(`2000-01-01T${startTime}`)
  const end = new Date(`2000-01-01T${endTime}`)

  const current = new Date(start)

  while (current < end) {
    const timeString = current.toTimeString().slice(0, 5)
    slots.push(timeString)
    current.setMinutes(current.getMinutes() + interval)
  }

  return slots
}

export function calculateClassEndTime(startTime: string, duration: number): string {
  const start = new Date(`2000-01-01T${startTime}`)
  start.setMinutes(start.getMinutes() + duration)
  return start.toTimeString().slice(0, 5)
}

export function isTimeSlotAvailable(slot: string, bookedSlots: string[], duration: number): boolean {
  const slotStart = new Date(`2000-01-01T${slot}`)
  const slotEnd = new Date(slotStart)
  slotEnd.setMinutes(slotEnd.getMinutes() + duration)

  return !bookedSlots.some((bookedSlot) => {
    const bookedStart = new Date(`2000-01-01T${bookedSlot}`)
    const bookedEnd = new Date(bookedStart)
    bookedEnd.setMinutes(bookedEnd.getMinutes() + duration)

    return slotStart < bookedEnd && slotEnd > bookedStart
  })
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-$$$$]{10,}$/
  return phoneRegex.test(phone)
}

export function generateBookingReference(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `PF-${timestamp}-${random}`.toUpperCase()
}

export function calculateMembershipDiscount(
  membershipType: "basic" | "premium" | "unlimited",
  originalPrice: number,
): number {
  const discounts = {
    basic: 0.05, // 5%
    premium: 0.15, // 15%
    unlimited: 0.25, // 25%
  }

  return originalPrice * (1 - discounts[membershipType])
}

export function getNextAvailableDate(
  schedule: Array<{ date: Date; availableSpots: number }>,
  requiredSpots = 1,
): Date | null {
  const availableDate = schedule.find((slot) => slot.availableSpots >= requiredSpots && slot.date > new Date())

  return availableDate?.date || null
}

export function groupBookingsByMonth(bookings: Array<{ date: Date }>): Record<string, number> {
  return bookings.reduce(
    (acc, booking) => {
      const monthKey = booking.date.toISOString().slice(0, 7) // YYYY-MM
      acc[monthKey] = (acc[monthKey] || 0) + 1
      return acc
    },
    {} as Record<string, number>,
  )
}

export function calculateAverageRating(reviews: Array<{ rating: number }>): number {
  if (reviews.length === 0) return 0
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0)
  return Math.round((sum / reviews.length) * 10) / 10
}

export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout

  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}
