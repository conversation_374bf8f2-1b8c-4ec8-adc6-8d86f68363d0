import { useState, useEffect, useCallback } from 'react';
import { classApi, locationApi, facilityApi, staffApi } from '@/lib/api/endpoints';
import { Class, Location, Facility, Staff } from '@/types/class';

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  loading: boolean;
  error: string | null;
}

// Cache storage
const cache = {
  classes: new Map<string, CacheEntry<Class>>(),
  locations: new Map<string, CacheEntry<Location>>(),
  facilities: new Map<string, CacheEntry<Facility>>(),
  staff: new Map<string, CacheEntry<Staff>>(),
};

// Cache TTL (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

// Helper function to check if cache entry is valid
function isCacheValid<T>(entry: CacheEntry<T> | undefined): boolean {
  if (!entry) return false;
  return Date.now() - entry.timestamp < CACHE_TTL;
}

// Custom hook for fetching and caching schedule-related data
export function useScheduleData() {
  const [loading, setLoading] = useState(false);

  // Get class data with caching
  const getClassData = useCallback(async (classId: string): Promise<Class | null> => {
    if (!classId) return null;

    // Check cache first
    const cached = cache.classes.get(classId);
    if (isCacheValid(cached)) {
      console.log('📦 Using cached class data:', classId.slice(-8));
      return cached.data;
    }

    // Set loading state
    cache.classes.set(classId, {
      data: {} as Class,
      timestamp: Date.now(),
      loading: true,
      error: null,
    });

    try {
      console.log('🌐 Fetching class data from API:', classId.slice(-8));
      const classData = await classApi.getClassById(classId);
      
      // Update cache
      cache.classes.set(classId, {
        data: classData,
        timestamp: Date.now(),
        loading: false,
        error: null,
      });

      return classData;
    } catch (error) {
      console.error('❌ Error fetching class data:', error);
      
      // Update cache with error
      cache.classes.set(classId, {
        data: {} as Class,
        timestamp: Date.now(),
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return null;
    }
  }, []);

  // Get location data with caching
  const getLocationData = useCallback(async (locationId: string): Promise<Location | null> => {
    if (!locationId) return null;

    const cached = cache.locations.get(locationId);
    if (isCacheValid(cached)) {
      console.log('📦 Using cached location data:', locationId.slice(-8));
      return cached.data;
    }

    try {
      console.log('🌐 Fetching location data from API:', locationId.slice(-8));
      const locationData = await locationApi.getLocationById(locationId);
      
      cache.locations.set(locationId, {
        data: locationData,
        timestamp: Date.now(),
        loading: false,
        error: null,
      });

      return locationData;
    } catch (error) {
      console.error('❌ Error fetching location data:', error);
      return null;
    }
  }, []);

  // Get facility data with caching
  const getFacilityData = useCallback(async (facilityId: string): Promise<Facility | null> => {
    if (!facilityId) return null;

    const cached = cache.facilities.get(facilityId);
    if (isCacheValid(cached)) {
      console.log('📦 Using cached facility data:', facilityId.slice(-8));
      return cached.data;
    }

    try {
      console.log('🌐 Fetching facility data from API:', facilityId.slice(-8));
      const facilityData = await facilityApi.getFacilityById(facilityId);
      
      cache.facilities.set(facilityId, {
        data: facilityData,
        timestamp: Date.now(),
        loading: false,
        error: null,
      });

      return facilityData;
    } catch (error) {
      console.error('❌ Error fetching facility data:', error);
      return null;
    }
  }, []);

  // Get staff data with caching
  const getStaffData = useCallback(async (staffId: string): Promise<Staff | null> => {
    if (!staffId) return null;

    const cached = cache.staff.get(staffId);
    if (isCacheValid(cached)) {
      console.log('📦 Using cached staff data:', staffId.slice(-8));
      return cached.data;
    }

    try {
      console.log('🌐 Fetching staff data from API:', staffId.slice(-8));
      const staffData = await staffApi.getStaffById(staffId);
      
      cache.staff.set(staffId, {
        data: staffData,
        timestamp: Date.now(),
        loading: false,
        error: null,
      });

      return staffData;
    } catch (error) {
      console.error('❌ Error fetching staff data:', error);
      return null;
    }
  }, []);

  // Clear cache function
  const clearCache = useCallback(() => {
    cache.classes.clear();
    cache.locations.clear();
    cache.facilities.clear();
    cache.staff.clear();
    console.log('🗑️ Cache cleared');
  }, []);

  return {
    getClassData,
    getLocationData,
    getFacilityData,
    getStaffData,
    clearCache,
    loading,
  };
}
