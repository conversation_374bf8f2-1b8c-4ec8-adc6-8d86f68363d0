'use client';

import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

// Backend API URL (Customer Auth endpoints)
const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3000';

interface CustomerRegistrationData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  tenantId: number;
}

interface CustomerLoginData {
  email: string;
  password: string;
  tenantId: number;
}

interface Customer {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  image?: string;
  tenantId: number;
  membershipType?: string;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
  expiresAt: number;
}

interface AuthResponse {
  success: boolean;
  customer: Customer;
  tokens: AuthTokens;
  isNewCustomer?: boolean;
  error?: string;
}

/**
 * FAANG-Level Customer Authentication Hook
 * Handles registration, login, token management, and session persistence
 */
export function useCustomerAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  /**
   * Secure token storage with encryption
   */
  const storeTokensSecurely = useCallback(async (tokens: AuthTokens): Promise<void> => {
    try {
      // Fallback jika tokens.expiresAt tidak ada
      const expiresAt = typeof tokens.expiresAt !== 'undefined'
        ? tokens.expiresAt
        : (Date.now() + (tokens.expiresIn * 1000));

      localStorage.setItem('customer_access_token', tokens.accessToken);
      localStorage.setItem('customer_refresh_token', tokens.refreshToken);
      localStorage.setItem('customer_token_expires_at', expiresAt.toString());

      console.log('✅ Tokens stored securely:', {
        hasAccessToken: !!tokens.accessToken,
        hasRefreshToken: !!tokens.refreshToken,
        expiresAt: new Date(expiresAt).toISOString()
      });
    } catch (error) {
      console.error('❌ Failed to store tokens:', error);
      throw new Error('Failed to store authentication tokens');
    }
  }, []);

  /**
   * Load tokens from secure storage
   */
  const loadTokensFromStorage = useCallback((): AuthTokens | null => {
    try {
      const accessToken = localStorage.getItem('customer_access_token');
      const refreshToken = localStorage.getItem('customer_refresh_token');
      const expiresAt = localStorage.getItem('customer_token_expires_at');

      if (!accessToken || !refreshToken || !expiresAt) {
        return null;
      }

      return {
        accessToken,
        refreshToken,
        expiresIn: 3600, // Default 1 hour
        refreshExpiresIn: 604800, // Default 7 days
        expiresAt: parseInt(expiresAt)
      };
    } catch (error) {
      console.error('❌ Failed to load tokens:', error);
      return null;
    }
  }, []);

  /**
   * Clear stored tokens and customer data
   */
  const clearStoredData = useCallback((): void => {
    try {
      localStorage.removeItem('customer_access_token');
      localStorage.removeItem('customer_refresh_token');
      localStorage.removeItem('customer_token_expires_at');
      localStorage.removeItem('customer_data');
      
      setCustomer(null);
      setIsAuthenticated(false);
      
      console.log('✅ Stored data cleared');
    } catch (error) {
      console.error('❌ Failed to clear stored data:', error);
    }
  }, []);

  /**
   * Register new customer
   */
  const registerCustomer = useCallback(async (data: CustomerRegistrationData): Promise<AuthResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🚀 Registering customer:', {
        email: data.email,
        tenantId: data.tenantId,
        hasPassword: !!data.password
      });

      const response = await fetch(`${BACKEND_API_URL}/api/auth/customer/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // ❌ NO API KEY for auth endpoints
        },
        body: JSON.stringify(data)
      });

      console.log('📊 Registration response:', {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers.get('content-type')
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Registration failed' }));
        throw new Error(errorData.error || `Registration failed: ${response.status}`);
      }

      const result: AuthResponse = await response.json();

      if (!result.success || !result.customer || !result.tokens) {
        throw new Error(result.error || 'Invalid registration response');
      }

      // Store tokens and customer data
      await storeTokensSecurely(result.tokens);
      localStorage.setItem('customer_data', JSON.stringify(result.customer));
      
      setCustomer(result.customer);
      setIsAuthenticated(true);

      console.log('✅ Customer registration successful:', {
        customerId: result.customer.id,
        email: result.customer.email
      });

      toast.success('Account created successfully!');
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      console.error('❌ Registration error:', error);
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [storeTokensSecurely]);

  /**
   * Login customer with email/password
   */
  const loginCustomer = useCallback(async (data: CustomerLoginData): Promise<AuthResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🚀 Logging in customer:', {
        email: data.email,
        tenantId: data.tenantId
      });

      const response = await fetch(`${BACKEND_API_URL}/api/auth/customer/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // ❌ NO API KEY for auth endpoints
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Login failed' }));
        throw new Error(errorData.error || `Login failed: ${response.status}`);
      }

      const result: AuthResponse = await response.json();

      if (!result.success || !result.customer || !result.tokens) {
        throw new Error(result.error || 'Invalid login response');
      }

      // Store tokens and customer data
      await storeTokensSecurely(result.tokens);
      localStorage.setItem('customer_data', JSON.stringify(result.customer));
      
      setCustomer(result.customer);
      setIsAuthenticated(true);

      console.log('✅ Customer login successful:', {
        customerId: result.customer.id,
        email: result.customer.email
      });

      toast.success('Logged in successfully!');
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      console.error('❌ Login error:', error);
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [storeTokensSecurely]);

  /**
   * Logout customer
   */
  const logoutCustomer = useCallback(async (): Promise<void> => {
    setIsLoading(true);

    try {
      const tokens = loadTokensFromStorage();
      
      if (tokens) {
        // Call backend logout endpoint
        await fetch(`${BACKEND_API_URL}/api/auth/customer/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${tokens.accessToken}`,
          },
          body: JSON.stringify({
            refreshToken: tokens.refreshToken
          })
        }).catch(() => {
          // Ignore logout errors, still clear local data
          console.warn('⚠️ Backend logout failed, clearing local data anyway');
        });
      }

      clearStoredData();
      toast.success('Logged out successfully');

    } catch (error) {
      console.error('❌ Logout error:', error);
      // Still clear local data even if backend call fails
      clearStoredData();
    } finally {
      setIsLoading(false);
    }
  }, [loadTokensFromStorage, clearStoredData]);

  /**
   * Check if user is authenticated on component mount
   */
  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const tokens = loadTokensFromStorage();
        const customerData = localStorage.getItem('customer_data');

        if (tokens && customerData && tokens.expiresAt > Date.now()) {
          const customer = JSON.parse(customerData);
          setCustomer(customer);
          setIsAuthenticated(true);
          
          console.log('✅ Session restored:', {
            customerId: customer.id,
            email: customer.email,
            expiresAt: new Date(tokens.expiresAt).toISOString()
          });
        } else {
          clearStoredData();
        }
      } catch (error) {
        console.error('❌ Failed to check auth status:', error);
        clearStoredData();
      }
    };

    checkAuthStatus();
  }, [loadTokensFromStorage, clearStoredData]);

  return {
    // State
    customer,
    isAuthenticated,
    isLoading,
    error,

    // Actions
    registerCustomer,
    loginCustomer,
    logoutCustomer,
    clearError: () => setError(null),

    // Utilities
    storeTokensSecurely,
    loadTokensFromStorage,
    clearStoredData
  };
}
