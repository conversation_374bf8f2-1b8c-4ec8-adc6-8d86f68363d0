import { useState, useEffect, useCallback, useRef } from 'react';
import { packagePricingApi } from '@/lib/api/endpoints';
import { PackagePricing, PackagePricingListState, PackagePricingFilters } from '@/types/package-pricing';
import { ApiError } from '@/lib/api/client';

interface UsePackagePricingOptions {
  enabled?: boolean;
  refetchInterval?: number;
}

interface UsePackagePricingReturn extends PackagePricingListState {
  refetch: () => Promise<void>;
  updateFilters: (newFilters: Partial<PackagePricingFilters>) => void;
  reset: () => void;
}

const DEFAULT_FILTERS: PackagePricingFilters = {
  isActive: true,
};

export function usePackagePricing(options: UsePackagePricingOptions = {}): UsePackagePricingReturn {
  const { enabled = true, refetchInterval } = options;
  
  const [state, setState] = useState<PackagePricingListState>({
    packages: [],
    loading: false,
    error: null,
    total: 0,
    filters: DEFAULT_FILTERS,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchPackages = useCallback(async () => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
    }));

    try {
      const response = await packagePricingApi.getPackages(
        abortControllerRef.current.signal
      );

      let filteredPackages = response.data;

      // Apply client-side filters
      if (state.filters.isActive !== undefined) {
        filteredPackages = filteredPackages.filter(pkg => pkg.isActive === state.filters.isActive);
      }

      if (state.filters.currency) {
        filteredPackages = filteredPackages.filter(pkg => 
          pkg.currency.toLowerCase() === state.filters.currency?.toLowerCase()
        );
      }

      if (state.filters.minPrice !== undefined) {
        filteredPackages = filteredPackages.filter(pkg => pkg.price >= state.filters.minPrice!);
      }

      if (state.filters.maxPrice !== undefined) {
        filteredPackages = filteredPackages.filter(pkg => pkg.price <= state.filters.maxPrice!);
      }

      if (state.filters.search) {
        const searchTerm = state.filters.search.toLowerCase();
        filteredPackages = filteredPackages.filter(pkg =>
          pkg.packageName.toLowerCase().includes(searchTerm) ||
          pkg.packageDescription.toLowerCase().includes(searchTerm)
        );
      }

      setState(prev => ({
        ...prev,
        packages: filteredPackages,
        total: filteredPackages.length,
        loading: false,
      }));
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        return; // Request was aborted, don't update state
      }

      const errorMessage = error instanceof ApiError 
        ? error.message 
        : 'Failed to load package pricing';

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));

      // Log error for monitoring
      console.error('Package pricing fetch error:', error);
    }
  }, [state.filters]);

  const updateFilters = useCallback((newFilters: Partial<PackagePricingFilters>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...newFilters },
    }));
  }, []);

  const reset = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: DEFAULT_FILTERS,
      packages: [],
      error: null,
    }));
  }, []);

  const refetch = useCallback(async () => {
    await fetchPackages();
  }, [fetchPackages]);

  // Initial fetch and filter changes
  useEffect(() => {
    if (enabled) {
      fetchPackages();
    }
  }, [enabled, fetchPackages]);

  // Auto-refetch interval
  useEffect(() => {
    if (refetchInterval && enabled) {
      intervalRef.current = setInterval(fetchPackages, refetchInterval);
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [refetchInterval, enabled, fetchPackages]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    ...state,
    refetch,
    updateFilters,
    reset,
  };
}
