'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  Customer, 
  TokenPair, 
  GoogleOAuthCallbackRequest,
  BookingContext 
} from '@/types/auth';
import { authApi } from '@/lib/api/endpoints';
import { 
  storeTokensSecurely, 
  clearStoredTokens, 
  hasValidSession,
  getAccessToken 
} from '@/lib/utils/token-storage';
import { getDeviceContext } from '@/lib/utils/device';
import { toast } from 'sonner';

// Default tenant ID
const DEFAULT_TENANT_ID = 1;

/**
 * Hook for handling Google OAuth callback
 */
export function useGoogleOAuthCallback() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleCallback = useCallback(async () => {
    if (isProcessing) return;

    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    // Handle OAuth error
    if (error) {
      console.error('OAuth error:', error);
      setError('Authentication was cancelled or failed');
      toast.error('Authentication failed. Please try again.');
      router.push('/auth/signin');
      return;
    }

    // Validate required parameters
    if (!code || !state) {
      console.error('Missing OAuth parameters');
      setError('Invalid authentication response');
      toast.error('Authentication failed. Please try again.');
      router.push('/auth/signin');
      return;
    }

    // Validate state parameter
    const storedState = sessionStorage.getItem('oauth_state');
    if (state !== storedState) {
      console.error('OAuth state mismatch');
      setError('Authentication failed due to security check');
      toast.error('Authentication failed. Please try again.');
      router.push('/auth/signin');
      return;
    }

    setIsProcessing(true);

    try {
      const deviceContext = getDeviceContext();

      const callbackRequest: GoogleOAuthCallbackRequest = {
        code,
        state,
        deviceType: deviceContext.deviceType,
        deviceId: deviceContext.deviceId,
      };

      const response = await authApi.handleGoogleCallback(callbackRequest);

      if (response.success) {
        // Store tokens securely
        await storeTokensSecurely(response.tokens);

        // Clear OAuth state
        sessionStorage.removeItem('oauth_state');

        // Show success message
        if (response.isNewCustomer) {
          toast.success('Welcome! Your account has been created.');
        } else {
          toast.success('Welcome back!');
        }

        // Check for booking context
        const bookingContext = sessionStorage.getItem('booking_context');
        if (bookingContext) {
          try {
            const context = JSON.parse(bookingContext);
            sessionStorage.removeItem('booking_context');
            
            // Redirect to booking flow
            router.push(`/schedule?booking=${context.scheduleId}`);
            return;
          } catch {
            // Invalid booking context, continue to home
          }
        }

        // Redirect to home page
        router.push('/');
      } else {
        throw new Error('Authentication failed');
      }

    } catch (error: any) {
      console.error('OAuth callback error:', error);
      setError(error.message || 'Authentication failed');
      toast.error('Authentication failed. Please try again.');
      router.push('/auth/signin');
    } finally {
      setIsProcessing(false);
    }
  }, [isProcessing, searchParams, router]);

  return {
    isProcessing,
    error,
    handleCallback,
  };
}

/**
 * Hook for session management
 */
export function useSession() {
  const [isValidating, setIsValidating] = useState(false);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const validateSession = useCallback(async (): Promise<boolean> => {
    try {
      setIsValidating(true);

      // Check if we have valid tokens
      const hasValid = await hasValidSession();
      if (!hasValid) {
        setIsAuthenticated(false);
        setCustomer(null);
        return false;
      }

      // Validate with server
      const validation = await authApi.validateSession();
      
      if (validation.valid && validation.customer) {
        setIsAuthenticated(true);
        setCustomer(validation.customer);
        return true;
      } else {
        setIsAuthenticated(false);
        setCustomer(null);
        await clearStoredTokens();
        return false;
      }

    } catch (error) {
      console.error('Session validation error:', error);
      setIsAuthenticated(false);
      setCustomer(null);
      await clearStoredTokens();
      return false;
    } finally {
      setIsValidating(false);
    }
  }, []);

  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      // Get current tokens
      const tokens = await getAccessToken();
      if (!tokens) return false;

      // Refresh tokens
      const deviceContext = getDeviceContext();
      const response = await authApi.refreshToken({
        refreshToken: tokens,
        deviceType: deviceContext.deviceType,
        deviceId: deviceContext.deviceId,
      });

      if (response.success) {
        await storeTokensSecurely(response.tokens);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Session refresh error:', error);
      return false;
    }
  }, []);

  return {
    isValidating,
    customer,
    isAuthenticated,
    validateSession,
    refreshSession,
  };
}

/**
 * Hook for protected routes
 */
export function useProtectedRoute() {
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  const checkAuth = useCallback(async () => {
    try {
      setIsChecking(true);

      const hasValid = await hasValidSession();
      
      if (hasValid) {
        const validation = await authApi.validateSession();
        
        if (validation.valid) {
          setIsAuthorized(true);
          return;
        }
      }

      // Not authenticated, redirect to sign in
      setIsAuthorized(false);
      router.push('/auth/signin');

    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthorized(false);
      router.push('/auth/signin');
    } finally {
      setIsChecking(false);
    }
  }, [router]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return {
    isChecking,
    isAuthorized,
  };
}

/**
 * Hook for booking authentication flow
 */
export function useBookingAuth() {
  const router = useRouter();

  const requireAuthForBooking = useCallback((scheduleId: string, returnUrl?: string): boolean => {
    // Check if user is authenticated
    const checkAuth = async () => {
      const hasValid = await hasValidSession();
      
      if (hasValid) {
        const validation = await authApi.validateSession();
        return validation.valid;
      }
      
      return false;
    };

    checkAuth().then(isAuthenticated => {
      if (!isAuthenticated) {
        // Store booking context
        const bookingContext: BookingContext = {
          scheduleId,
          returnUrl,
        };

        sessionStorage.setItem('booking_context', JSON.stringify({
          ...bookingContext,
          timestamp: Date.now(),
        }));

        // Redirect to sign in
        router.push('/auth/signin');
      }
    });

    return true; // Always return true to indicate auth check is in progress
  }, [router]);

  const getBookingContext = useCallback((): BookingContext | null => {
    try {
      const stored = sessionStorage.getItem('booking_context');
      if (!stored) return null;

      const context = JSON.parse(stored);

      // Check if context is not too old (30 minutes)
      if (Date.now() - context.timestamp > 30 * 60 * 1000) {
        sessionStorage.removeItem('booking_context');
        return null;
      }

      return {
        scheduleId: context.scheduleId,
        returnUrl: context.returnUrl,
        bookingData: context.bookingData,
      };
    } catch {
      return null;
    }
  }, []);

  const clearBookingContext = useCallback(() => {
    sessionStorage.removeItem('booking_context');
  }, []);

  return {
    requireAuthForBooking,
    getBookingContext,
    clearBookingContext,
  };
}

/**
 * Hook for automatic token refresh
 */
export function useTokenRefresh() {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshTokens = useCallback(async (): Promise<boolean> => {
    if (isRefreshing) return false;

    try {
      setIsRefreshing(true);

      const hasValid = await hasValidSession();
      if (!hasValid) return false;

      const deviceContext = getDeviceContext();
      const tokens = await getAccessToken();
      
      if (!tokens) return false;

      const response = await authApi.refreshToken({
        refreshToken: tokens,
        deviceType: deviceContext.deviceType,
        deviceId: deviceContext.deviceId,
      });

      if (response.success) {
        await storeTokensSecurely(response.tokens);
        return true;
      }

      return false;

    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing]);

  // Auto-refresh setup
  useEffect(() => {
    const setupAutoRefresh = async () => {
      const hasValid = await hasValidSession();
      if (!hasValid) return;

      // Check token expiry every minute
      const interval = setInterval(async () => {
        const tokens = await getAccessToken();
        if (!tokens) return;

        // Refresh if token expires in next 5 minutes
        const expiresIn = 5 * 60 * 1000; // 5 minutes
        const shouldRefresh = Date.now() + expiresIn > Date.now();

        if (shouldRefresh) {
          await refreshTokens();
        }
      }, 60 * 1000); // Check every minute

      return () => clearInterval(interval);
    };

    setupAutoRefresh();
  }, [refreshTokens]);

  return {
    isRefreshing,
    refreshTokens,
  };
}
