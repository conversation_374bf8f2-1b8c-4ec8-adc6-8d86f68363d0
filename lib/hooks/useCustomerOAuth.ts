
'use client';
/**
 * Store tokens and customer data in localStorage (for OAuth login)
 * Mirrors logic from useCustomerAuth
 */
import { storeTokensSecurely } from '@/lib/utils/token-storage';
export async function storeOAuthSession(tokens: {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
  expiresAt?: number;
}, customer: any) {
  try {
    // Fallback jika tokens.expiresAt tidak ada
    const expiresAt = typeof tokens.expiresAt !== 'undefined'
      ? tokens.expiresAt
      : (Date.now() + (tokens.expiresIn * 1000));

    // Simpan token ke localStorage dengan key yang sama seperti useCustomerAuth
    localStorage.setItem('customer_access_token', tokens.accessToken);
    localStorage.setItem('customer_refresh_token', tokens.refreshToken);
    localStorage.setItem('customer_token_expires_at', expiresAt.toString());
    localStorage.setItem('customer_data', JSON.stringify(customer));
    console.log('✅ [OAuth] Tokens & customer stored (compat):', {
      hasAccessToken: !!tokens.accessToken,
      hasRefreshToken: !!tokens.refreshToken,
      expiresAt: new Date(expiresAt).toISOString(),
      customerId: customer?.id
    });
  } catch (error) {
    console.error('❌ [OAuth] Failed to store tokens:', error);
    throw new Error('Failed to store authentication tokens');
  }
}

import { useState, useCallback } from 'react';
import { toast } from 'sonner';

// Backend API URL (Customer OAuth endpoints)
const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3000';
const NEXT_API_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

interface OAuthInitRequest {
  tenantId: number;
  clientType: 'web' | 'mobile';
  deviceId: string;
  redirectUri: string;
}

interface OAuthInitResponse {
  success: boolean;
  authUrl: string;
  state: string;
  codeVerifier?: string;
  error?: string;
}

interface OAuthCallbackRequest {
  code: string;
  state: string;
  tenantId: number;
  clientType: 'web' | 'mobile';
  deviceId: string;
  codeVerifier?: string;
}

interface OAuthCallbackResponse {
  success: boolean;
  customer: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    displayName: string;
    image?: string;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    refreshExpiresIn: number;
  };
  isNewCustomer: boolean;
  error?: string;
}

/**
 * FAANG-Level Customer OAuth Hook
 * Handles Google OAuth flow with PKCE, secure state management, and comprehensive error handling
 */
export function useCustomerOAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Generate secure device ID for OAuth flow
   */
  const generateDeviceId = useCallback((): string => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `web-${timestamp}-${random}`;
  }, []);

  /**
   * Generate secure state for OAuth flow
   */
  const generateSecureState = useCallback((): string => {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }, []);

  /**
   * Initiate Google OAuth flow
   * Calls backend Customer OAuth init endpoint
   */
  const initiateGoogleOAuth = useCallback(async (tenantId: number = 1): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      // Generate secure parameters

      const deviceId = generateDeviceId();
      // Hardcode redirectUri ke port 3000 agar selalu cocok dengan Google Cloud Console
      const redirectUri = 'http://localhost:3000/api/auth/customer/google/callback';

      console.log('🚀 Initiating Customer OAuth flow:', {
        tenantId,
        deviceId: deviceId.substring(0, 15) + '...',
        redirectUri,
        backendUrl: BACKEND_API_URL
      });

      const request: OAuthInitRequest = {
        tenantId,
        clientType: 'web',
        deviceId,
        redirectUri
      };

      // Call backend Customer OAuth init endpoint (NO API KEY)
      const response = await fetch(`${BACKEND_API_URL}/api/auth/customer/google/init`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // ❌ NO API KEY for OAuth endpoints
        },
        body: JSON.stringify(request)
      });

      console.log('📊 OAuth init response:', {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers.get('content-type')
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ OAuth init failed:', errorData);
        throw new Error(`OAuth initialization failed: ${response.status} ${response.statusText}`);
      }

      const data: OAuthInitResponse = await response.json();

      if (!data.success || !data.authUrl || !data.state) {
        console.error('❌ Invalid OAuth init response:', data);
        throw new Error(data.error || 'Invalid OAuth response from server');
      }

      console.log('✅ OAuth init successful:', {
        hasAuthUrl: !!data.authUrl,
        hasState: !!data.state,
        hasPKCE: !!data.codeVerifier
      });

      // Store OAuth state and parameters securely
      sessionStorage.setItem('oauth_state', data.state);
      sessionStorage.setItem('oauth_tenant_id', tenantId.toString());
      sessionStorage.setItem('oauth_device_id', deviceId);
      
      if (data.codeVerifier) {
        sessionStorage.setItem('oauth_code_verifier', data.codeVerifier);
      }

      // Redirect to Google OAuth
      console.log('🔄 Redirecting to Google OAuth...');
      window.location.href = data.authUrl;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown OAuth error';
      console.error('❌ OAuth initiation error:', error);
      setError(errorMessage);
      toast.error(`Authentication failed: ${errorMessage}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [generateDeviceId, generateSecureState]);

  /**
   * Handle OAuth callback
   * Exchanges authorization code for tokens
   */
  const handleOAuthCallback = useCallback(async (
    code: string,
    state: string
  ): Promise<OAuthCallbackResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate stored state
      const storedState = sessionStorage.getItem('oauth_state');
      const storedTenantId = sessionStorage.getItem('oauth_tenant_id');
      const storedDeviceId = sessionStorage.getItem('oauth_device_id');
      const storedCodeVerifier = sessionStorage.getItem('oauth_code_verifier');

      if (state !== storedState) {
        throw new Error('OAuth state mismatch - possible CSRF attack');
      }

      if (!storedTenantId || !storedDeviceId) {
        throw new Error('Missing OAuth session data');
      }

      console.log('🔄 Processing OAuth callback:', {
        hasCode: !!code,
        hasState: !!state,
        stateMatches: state === storedState,
        tenantId: storedTenantId,
        deviceId: storedDeviceId?.substring(0, 15) + '...',
        hasPKCE: !!storedCodeVerifier
      });

      const request: OAuthCallbackRequest = {
        code,
        state,
        tenantId: parseInt(storedTenantId),
        clientType: 'web',
        deviceId: storedDeviceId,
        ...(storedCodeVerifier && { codeVerifier: storedCodeVerifier })
      };

      // Call backend Customer OAuth callback endpoint (NO API KEY)
      const response = await fetch(`${BACKEND_API_URL}/api/auth/customer/google/callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // ❌ NO API KEY for OAuth endpoints
        },
        body: JSON.stringify(request)
      });

      console.log('📊 OAuth callback response:', {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers.get('content-type')
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ OAuth callback failed:', errorData);
        throw new Error(`OAuth callback failed: ${response.status} ${response.statusText}`);
      }

      const result: OAuthCallbackResponse = await response.json();

      if (!result.success || !result.tokens || !result.customer) {
        console.error('❌ Invalid OAuth callback response:', result);
        throw new Error(result.error || 'Invalid OAuth callback response');
      }

      console.log('✅ OAuth callback successful:', {
        customerId: result.customer.id,
        email: result.customer.email,
        isNewCustomer: result.isNewCustomer,
        hasTokens: !!result.tokens.accessToken
      });

      // Clean up session storage
      sessionStorage.removeItem('oauth_state');
      sessionStorage.removeItem('oauth_tenant_id');
      sessionStorage.removeItem('oauth_device_id');
      sessionStorage.removeItem('oauth_code_verifier');

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown OAuth callback error';
      console.error('❌ OAuth callback error:', error);
      setError(errorMessage);
      toast.error(`Authentication failed: ${errorMessage}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    initiateGoogleOAuth,
    handleOAuthCallback,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}
