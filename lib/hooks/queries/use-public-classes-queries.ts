// 🎣 Custom Hooks untuk Public Classes API
// Implementasi dengan TanStack Query dan caching strategy yang optimal

'use client';

import {useQuery, useInfiniteQuery, UseQueryOptions } from '@tanstack/react-query';
import { 
  PublicClassDTO, 
  PublicClassesParams,
  UsePublicClassesReturn,
  UsePublicClassReturn
} from '@/types/public-api';
import { publicClassesApi, PublicClassesApiError } from '@/lib/api/public-classes-client';

// Query Keys Factory - untuk cache management yang terstruktur
export const publicClassesKeys = {
  all: ['public-classes'] as const,
  lists: () => [...publicClassesKeys.all, 'list'] as const,
  list: (params: PublicClassesParams) => [...publicClassesKeys.lists(), params] as const,
  details: () => [...publicClassesKeys.all, 'detail'] as const,
  detail: (id: string) => [...publicClassesKeys.details(), id] as const,
  infinite: (params: Omit<PublicClassesParams, 'page'>) => [...publicClassesKeys.lists(), 'infinite', params] as const,
};

// Default cache configuration
const DEFAULT_CACHE_CONFIG = {
  staleTime: 5 * 60 * 1000,      // 5 menit - data dianggap fresh
  gcTime: 10 * 60 * 1000,        // 10 menit - cache cleanup
  refetchOnWindowFocus: false,    // Tidak refetch saat focus window
  refetchOnReconnect: true,       // Refetch saat koneksi kembali
  retry: (failureCount: number, error: any) => {
    // Jangan retry untuk client errors kecuali rate limit
    if (error instanceof PublicClassesApiError) {
      return error.isRetryable && failureCount < 3;
    }
    return failureCount < 3;
  },
} as const;

/**
 * Hook untuk mengambil daftar classes dengan filtering dan pagination
 */
export function usePublicClasses(
  params: PublicClassesParams = {},
  options?: Omit<UseQueryOptions<PublicClassDTO[]>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: publicClassesKeys.list(params),
    queryFn: () => publicClassesApi.getClasses(params),
    ...DEFAULT_CACHE_CONFIG,
    ...options,
  });
}

/**
 * Hook untuk mengambil detail class berdasarkan ID
 */
export function usePublicClass(
  id: string,
  options?: Omit<UseQueryOptions<PublicClassDTO>, 'queryKey' | 'queryFn'>
): UsePublicClassReturn {
  const query = useQuery({
    queryKey: publicClassesKeys.detail(id),
    queryFn: () => publicClassesApi.getClassById(id),
    enabled: !!id,
    ...DEFAULT_CACHE_CONFIG,
    ...options,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
  };
}

/**
 * Enhanced hook dengan error handling yang comprehensive
 */
export function usePublicClassesWithErrorHandling(
  params: PublicClassesParams = {}
): UsePublicClassesReturn {
  const query = usePublicClasses(params);
  
  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    
    // Enhanced error information
    errorMessage: query.error?.message || null,
    isUnauthorized: query.error instanceof PublicClassesApiError && query.error.code === 'UNAUTHORIZED',
    isNotFound: query.error instanceof PublicClassesApiError && query.error.code === 'NOT_FOUND',
    isNetworkError: query.error instanceof PublicClassesApiError && query.error.code === 'NETWORK_ERROR',
    
    // Helper methods
    retry: query.refetch,
    hasData: !!query.data && query.data.length > 0,
  };
}

/**
 * Hook untuk infinite scrolling/pagination
 */
export function useInfinitePublicClasses(
  params: Omit<PublicClassesParams, 'page'> = {}
) {
  return useInfiniteQuery({
    queryKey: publicClassesKeys.infinite(params),
    queryFn: async ({ pageParam = 1 }) => {
      const classes = await publicClassesApi.getClasses({
        ...params,
        page: pageParam,
      });
      
      return {
        classes,
        nextPage: classes.length === (params.limit || 20) ? pageParam + 1 : undefined,
        currentPage: pageParam,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    ...DEFAULT_CACHE_CONFIG,
  });
}

/**
 * Hook dengan real-time updates menggunakan polling
 */
export function usePublicClassesRealtime(
  params: PublicClassesParams = {},
  intervalMs: number = 30000 // 30 detik default
) {
  return usePublicClasses(params, {
    refetchInterval: intervalMs,
    refetchIntervalInBackground: false, // Hanya poll saat tab aktif
  });
}

/**
 * Hook dengan pagination helper
 */
export function usePublicClassesWithPagination(
  tenantId: string,
  limit: number = 20,
  page: number = 1
) {
  const params = { tenantId, limit, page };
  const query = usePublicClassesWithErrorHandling(params);

  return {
    ...query,
    pagination: {
      currentPage: page,
      limit,
      hasNextPage: query.hasData && (query.data?.length || 0) === limit,
      hasPreviousPage: page > 1,
    },
  };
}

/**
 * Hook untuk search functionality
 */
export function usePublicClassesSearch(
  searchTerm: string,
  tenantId?: string,
  debounceMs: number = 300
) {
  // Simple debounce implementation
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState(searchTerm);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  return usePublicClassesWithErrorHandling({
    tenantId,
    // Note: API belum support search, ini untuk future enhancement
    // search: debouncedSearchTerm,
  });
}

/**
 * Development hook dengan logging untuk debugging
 */
export function usePublicClassesDebug(params: PublicClassesParams = {}) {
  const query = usePublicClasses(params);

  // Log query state changes in development
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Public Classes Query State:', {
        isLoading: query.isLoading,
        isError: query.isError,
        isSuccess: query.isSuccess,
        dataLength: query.data?.length || 0,
        error: query.error?.message,
        queryKey: publicClassesKeys.list(params),
      });
    }
  }, [query.isLoading, query.isError, query.isSuccess, query.data, query.error, params]);

  return query;
}

/**
 * Hook dengan performance monitoring
 */
export function usePublicClassesWithMetrics(params: PublicClassesParams = {}) {
  const startTime = React.useRef<number>();
  const query = usePublicClasses(params);

  React.useEffect(() => {
    if (query.isLoading && !startTime.current) {
      startTime.current = Date.now();
    }

    if (query.isSuccess && startTime.current) {
      const duration = Date.now() - startTime.current;
      console.log(`⏱️ Public Classes API call took ${duration}ms`);

      // Send metrics to monitoring service (Google Analytics example)
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'api_call_duration', {
          event_category: 'performance',
          event_label: 'public_classes',
          value: duration,
        });
      }

      startTime.current = undefined;
    }
  }, [query.isLoading, query.isSuccess]);

  return query;
}

/**
 * Hook untuk prefetching data
 */
export function usePrefetchPublicClasses() {
  const queryClient = useQueryClient();

  return React.useCallback(
    (params: PublicClassesParams) => {
      queryClient.prefetchQuery({
        queryKey: publicClassesKeys.list(params),
        queryFn: () => publicClassesApi.getClasses(params),
        ...DEFAULT_CACHE_CONFIG,
      });
    },
    [queryClient]
  );
}

// Import React untuk hooks yang membutuhkan
import React from 'react';
import { useQueryClient } from '@tanstack/react-query';
