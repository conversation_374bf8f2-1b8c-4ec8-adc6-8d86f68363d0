import { useState, useEffect, useCallback, useRef } from 'react';
import { scheduleApi } from '@/lib/api/endpoints';
import { ClassSchedule, ScheduleFilters, ScheduleListState } from '@/types/schedule';
import { ApiError } from '@/lib/api/client';
import { useDebounce } from './useDebounce';

interface UseSchedulesOptions {
  initialFilters?: Partial<ScheduleFilters>;
  enabled?: boolean;
  refetchInterval?: number;
}

interface UseSchedulesReturn extends ScheduleListState {
  refetch: () => Promise<void>;
  loadMore: () => Promise<void>;
  updateFilters: (newFilters: Partial<ScheduleFilters>) => void;
  reset: () => void;
}

const DEFAULT_FILTERS: ScheduleFilters = {
  tenantId: 1,
  limit: 10,
  offset: 0,
};

export function useSchedules(options: UseSchedulesOptions = {}): UseSchedulesReturn {
  const { initialFilters = {}, enabled = true, refetchInterval } = options;
  
  const [state, setState] = useState<ScheduleListState>({
    schedules: [],
    loading: false,
    error: null,
    hasMore: false,
    total: 0,
    filters: { ...DEFAULT_FILTERS, ...initialFilters },
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const debouncedSearch = useDebounce(state.filters.search || '', 300);

  // Update filters and trigger search when debounced search changes
  useEffect(() => {
    if (debouncedSearch !== (state.filters.search || '')) {
      setState(prev => ({
        ...prev,
        filters: { ...prev.filters, search: debouncedSearch, offset: 0 },
      }));
    }
  }, [debouncedSearch, state.filters.search]);

  const fetchSchedules = useCallback(async (append = false) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
    }));

    try {
      const response = await scheduleApi.getSchedules(
        state.filters,
        abortControllerRef.current.signal
      );

      setState(prev => ({
        ...prev,
        schedules: append ? [...prev.schedules, ...response.data] : response.data,
        hasMore: response.hasMore,
        total: response.total,
        loading: false,
      }));
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        return; // Request was aborted, don't update state
      }

      const errorMessage = error instanceof ApiError 
        ? error.message 
        : 'Failed to load schedules';

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));

      // Log error for monitoring
      console.error('Schedule fetch error:', error);
    }
  }, [state.filters]);

  const loadMore = useCallback(async () => {
    if (!state.hasMore || state.loading) return;

    setState(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        offset: prev.schedules.length,
      },
    }));

    await fetchSchedules(true);
  }, [state.hasMore, state.loading, state.schedules.length, fetchSchedules]);

  const updateFilters = useCallback((newFilters: Partial<ScheduleFilters>) => {
    setState(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        ...newFilters,
        offset: 0, // Reset pagination
      },
      schedules: [], // Clear existing schedules
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      schedules: [],
      loading: false,
      error: null,
      hasMore: false,
      total: 0,
      filters: { ...DEFAULT_FILTERS, ...initialFilters },
    });
  }, [initialFilters]);

  const refetch = useCallback(async () => {
    setState(prev => ({ ...prev, schedules: [], filters: { ...prev.filters, offset: 0 } }));
    await fetchSchedules(false);
  }, [fetchSchedules]);

  // Initial load and filter changes
  useEffect(() => {
    if (!enabled) return;
    fetchSchedules(false);
  }, [enabled, state.filters, fetchSchedules]);

  // Polling/refetch interval
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(() => {
      refetch();
    }, refetchInterval);

    return () => clearInterval(interval);
  }, [refetchInterval, enabled, refetch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    refetch,
    loadMore,
    updateFilters,
    reset,
  };
}