// 🚀 Public Classes API Client
// Implementasi API client dengan authentication dan error handling yang robust

import { 
  PublicClassDTO, 
  PublicClassesResponse, 
  PublicClassResponse, 
  PublicClassesParams,
  PublicApiConfig,
  PublicClassesError
} from '@/types/public-api';

// API Configuration
const getApiConfig = (): PublicApiConfig => {
  const apiKey = process.env.NEXT_PUBLIC_CLASSES_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';
  const baseUrl = 'http://localhost:3000';

  if (!apiKey) {
    throw new Error(
      'NEXT_PUBLIC_CLASSES_API_KEY is not configured. ' +
      'Please add it to your environment variables.'
    );
  }

  if (process.env.NODE_ENV === 'development' && !apiKey.startsWith('pk_')) {
    console.warn(
      '⚠️ API key should start with "pk_" prefix for better security practices'
    );
  }

  return { apiKey, baseUrl };
};

// Rate limiting configuration
export const API_RATE_LIMITS = {
  requestsPerHour: 100,
  burstLimit: 10,
  retryAfterMs: 60000, // 1 minute
} as const;

// Enhanced error handling
class PublicClassesApiError extends Error {
  public status?: number;
  public code?: string;
  public isRetryable: boolean;

  constructor(message: string, status?: number, code?: string) {
    super(message);
    this.name = 'PublicClassesApiError';
    this.status = status;
    this.code = code;
    this.isRetryable = this.determineRetryability(status);
  }

  private determineRetryability(status?: number): boolean {
    if (!status) return true;
    
    // Don't retry client errors (4xx) except for 408, 429
    if (status >= 400 && status < 500) {
      return status === 408 || status === 429;
    }
    
    // Retry server errors (5xx) and network errors
    return status >= 500 || status === 0;
  }
}

// Secure API client with interceptors
const createSecureApiClient = () => {
  const { apiKey, baseUrl } = getApiConfig();

  return {
    async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
      const url = `${baseUrl}${endpoint}`;
      
      console.log('🚀 Making API request to:', url);

      try {
        const response = await fetch(url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': apiKey,
            // Security headers
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache',
            ...options.headers,
          },
        });

        console.log('📡 API Response:', {
          status: response.status,
          ok: response.ok,
          statusText: response.statusText
        });

        // Handle rate limiting
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After');
          throw new PublicClassesApiError(
            `Rate limit exceeded. Retry after ${retryAfter || '60'} seconds.`,
            429,
            'RATE_LIMIT_EXCEEDED'
          );
        }

        // Handle unauthorized
        if (response.status === 401) {
          throw new PublicClassesApiError(
            'API key tidak valid atau sudah expired',
            401,
            'UNAUTHORIZED'
          );
        }

        // Handle not found
        if (response.status === 404) {
          throw new PublicClassesApiError(
            'Endpoint tidak ditemukan',
            404,
            'NOT_FOUND'
          );
        }

        if (!response.ok) {
          throw new PublicClassesApiError(
            `API request failed: ${response.statusText}`,
            response.status,
            'API_ERROR'
          );
        }

        const data = await response.json();
        console.log('✅ API Success:', { success: data.success });
        
        return data;
      } catch (error) {
        console.error('❌ API Error:', error);
        
        if (error instanceof PublicClassesApiError) {
          throw error;
        }
        
        // Handle network errors
        if (error instanceof TypeError && error.message.includes('fetch')) {
          throw new PublicClassesApiError(
            'Koneksi bermasalah. Periksa internet Anda.',
            0,
            'NETWORK_ERROR'
          );
        }
        
        throw new PublicClassesApiError(
          error instanceof Error ? error.message : 'Unknown error occurred',
          undefined,
          'UNKNOWN_ERROR'
        );
      }
    },
  };
};

// API functions
export const publicClassesApi = {
  /**
   * Mengambil daftar classes dengan filtering dan pagination
   */
  async getClasses(params: PublicClassesParams = {}): Promise<PublicClassDTO[]> {
    const client = createSecureApiClient();
    const searchParams = new URLSearchParams();
    
    // Build query parameters
    if (params.tenantId) searchParams.append('tenantId', params.tenantId);
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.page) searchParams.append('page', params.page.toString());

    const endpoint = `/api/public/v1/classes${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    
    const response = await client.request<PublicClassesResponse>(endpoint);
    
    if (!response.success) {
      throw new PublicClassesApiError(
        response.error || 'Failed to fetch classes',
        undefined,
        'API_RESPONSE_ERROR'
      );
    }

    return response.data.classes;
  },

  /**
   * Mengambil detail class berdasarkan ID
   */
  async getClassById(id: string): Promise<PublicClassDTO> {
    const client = createSecureApiClient();
    
    const response = await client.request<PublicClassResponse>(
      `/api/public/v1/classes?id=${id}`
    );
    
    if (!response.success) {
      throw new PublicClassesApiError(
        response.error || 'Class not found',
        404,
        'CLASS_NOT_FOUND'
      );
    }

    return response.data;
  },

  /**
   * Health check untuk memastikan API tersedia
   */
  async healthCheck(): Promise<boolean> {
    try {
      const client = createSecureApiClient();
      await client.request('/api/public/v1/classes?limit=1');
      return true;
    } catch (error) {
      console.warn('API health check failed:', error);
      return false;
    }
  },
};

// Export error class untuk error handling di components
export { PublicClassesApiError };

// Export API config getter untuk debugging
export { getApiConfig };

// Development helpers
export const publicClassesApiDebug = {
  /**
   * Test API connection dengan logging detail
   */
  async testConnection(): Promise<void> {
    console.log('🧪 Testing Public Classes API connection...');
    
    try {
      const config = getApiConfig();
      console.log('📋 API Config:', { 
        baseUrl: config.baseUrl, 
        hasApiKey: !!config.apiKey,
        apiKeyPrefix: config.apiKey.substring(0, 8) + '...'
      });

      const isHealthy = await publicClassesApi.healthCheck();
      console.log('🏥 Health Check:', isHealthy ? '✅ Passed' : '❌ Failed');

      if (isHealthy) {
        const classes = await publicClassesApi.getClasses({ limit: 1 });
        console.log('📊 Sample Data:', classes.length > 0 ? '✅ Available' : '⚠️ Empty');
      }
    } catch (error) {
      console.error('🚨 Connection Test Failed:', error);
    }
  },

  /**
   * Log current API configuration
   */
  logConfig(): void {
    try {
      const config = getApiConfig();
      console.table({
        'Base URL': config.baseUrl,
        'API Key (masked)': config.apiKey.substring(0, 8) + '...',
        'Environment': process.env.NODE_ENV,
        'Rate Limit (per hour)': API_RATE_LIMITS.requestsPerHour,
      });
    } catch (error) {
      console.error('Failed to log config:', error);
    }
  },
};
