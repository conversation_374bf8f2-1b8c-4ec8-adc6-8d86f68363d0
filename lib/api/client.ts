import { ApiResponse, RequestConfig } from '@/types/api';
import { getApiBaseUrl, getExternalApiBaseUrl } from '@/lib/utils/environment';

class ApiClient {
  private baseURL: string;
  private defaultTimeout = 10000;
  private maxRetries = 3;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit & RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const { signal, timeout = this.defaultTimeout, retries = this.maxRetries, ...fetchOptions } = options;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const requestSignal = signal || controller.signal;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const fullUrl = `${this.baseURL}${endpoint}`;
        console.log('🔍 API Request:', { fullUrl, method: fetchOptions.method || 'GET' });

        const response = await fetch(fullUrl, {
          ...fetchOptions,
          signal: requestSignal,
          headers: {
            'Content-Type': 'application/json',
            ...fetchOptions.headers,
          },
        });

        clearTimeout(timeoutId);

        console.log('📡 API Response:', {
          url: fullUrl,
          status: response.status,
          ok: response.ok,
          statusText: response.statusText
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error('❌ API Error:', { fullUrl, status: response.status, errorData });
          throw new ApiError(
            errorData.error || `HTTP ${response.status}: ${response.statusText}`,
            response.status,
            errorData.code,
            errorData.details
          );
        }

        const data: ApiResponse<T> = await response.json();
        return data;
        
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on abort or client errors (4xx)
        if (
          error instanceof DOMException && error.name === 'AbortError' ||
          (error as ApiError).status >= 400 && (error as ApiError).status < 500
        ) {
          break;
        }
        
        // Wait before retry (exponential backoff)
        if (attempt < retries) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }
    
    clearTimeout(timeoutId);
    throw lastError || new Error('Request failed');
  }

  async get<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'GET', ...config });
  }

  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
  }
}

// Custom error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// External API client (backend service)
export const apiClient = new ApiClient(getExternalApiBaseUrl());

// Internal API client for Next.js API routes (relative URLs)
export const internalApiClient = new ApiClient('');