import { apiClient, internalApiClient } from './client';
import { ClassSchedule, ScheduleFilters } from '@/types/schedule';
import { PackagePricing, PackagePricingResponse } from '@/types/package-pricing';
import { Class, Location, Facility, Staff } from '@/types/class';
import { PaginatedResponse } from '@/types/api';
import {
  GoogleOAuthInitRequest,
  GoogleOAuthInitResponse,
  GoogleOAuthCallbackRequest,
  GoogleOAuthCallbackResponse,
  CustomerLoginRequest,
  CustomerLoginResponse,
  TokenRefreshRequest,
  TokenRefreshResponse,
  Customer,
} from '@/types/auth';


const API_KEY = process.env.PACKAGE_PRICING_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';

export const scheduleApi = {
  getSchedules: async (
    filters: ScheduleFilters,
    signal?: AbortSignal
  ): Promise<PaginatedResponse<ClassSchedule>> => {
    const params = new URLSearchParams();

    // Required parameter
    params.append('tenantId', filters.tenantId.toString());

    // Optional parameters
    if (filters.search) params.append('search', filters.search);
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.locationId) params.append('locationId', filters.locationId);
    if (filters.facilityId) params.append('facilityId', filters.facilityId);
    if (filters.staffId) params.append('staffId', filters.staffId);
    if (filters.isPrivate !== undefined) params.append('isPrivate', filters.isPrivate.toString());
    if (filters.allowClasspass !== undefined) params.append('allowClasspass', filters.allowClasspass.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.offset) params.append('offset', filters.offset.toString());

    const response = await apiClient.get<{ schedules: ClassSchedule[]; total: number; hasMore: boolean }>(
      `/api/public/v1/class-schedules-with-details?${params.toString()}`,
      {
        signal,
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY,
        },
      });
    // Map API response to PaginatedResponse<ClassSchedule>
    return {
      data: response.data.schedules,
      total: response.data.total,
      hasMore: response.data.hasMore,
      limit: filters.limit ?? 0,
      offset: filters.offset ?? 0,
    };
  },
};

export const packagePricingApi = {
  getPackages: async (signal?: AbortSignal): Promise<PackagePricingResponse> => {
    try {
      console.log('🚀 Starting package pricing API call...');

      // Gunakan base URL backend jika ada
      const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3000';
      const url = `${BACKEND_API_URL}/api/public/v1/package-pricing`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY,
        },
        signal,
      });

      console.log('📡 Direct fetch response:', {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Direct fetch error:', { status: response.status, errorText });
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Direct fetch success:', { success: data.success, total: data.total });

      return {
        success: data.success,
        data: data.data || [],
        total: data.total || 0,
        error: data.error,
      };
    } catch (error) {
      console.error('💥 Package pricing API error:', error);
      throw error;
    }
  },

  getPackageById: async (id: string, signal?: AbortSignal): Promise<PackagePricing> => {
    const response = await internalApiClient.get<PackagePricing>(`/api/public/v1/package-pricing/${id}`, { signal });
    return response.data;
  },
};

// Class API
export const classApi = {
  getClassById: async (id: string, signal?: AbortSignal): Promise<Class> => {
    try {
      console.log('🔍 Fetching class by ID:', id);
      const response = await fetch(`/api/classes/${id}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Class data received:', { success: data.success, fallback: data.fallback });

      return data.data;
    } catch (error) {
      console.error('❌ Class API error:', error);
      throw error;
    }
  },
};

// Location API
export const locationApi = {
  getLocationById: async (id: string, signal?: AbortSignal): Promise<Location> => {
    try {
      console.log('🔍 Fetching location by ID:', id);
      const response = await fetch(`/api/locations/${id}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Location data received:', { success: data.success, fallback: data.fallback });

      return data.data;
    } catch (error) {
      console.error('❌ Location API error:', error);
      throw error;
    }
  },
};

// Facility API
export const facilityApi = {
  getFacilityById: async (id: string, signal?: AbortSignal): Promise<Facility> => {
    try {
      console.log('🔍 Fetching facility by ID:', id);
      const response = await fetch(`/api/facilities/${id}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Facility data received:', { success: data.success, fallback: data.fallback });

      return data.data;
    } catch (error) {
      console.error('❌ Facility API error:', error);
      throw error;
    }
  },
};

// Staff API
export const staffApi = {
  getStaffById: async (id: string, signal?: AbortSignal): Promise<Staff> => {
    try {
      console.log('🔍 Fetching staff by ID:', id);
      const response = await fetch(`/api/staff/${id}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Staff data received:', { success: data.success, fallback: data.fallback });

      return data.data;
    } catch (error) {
      console.error('❌ Staff API error:', error);
      throw error;
    }
  },
};

// Authentication API
export const authApi = {
  // Initialize Google OAuth flow
  initGoogleOAuth: async (request: GoogleOAuthInitRequest): Promise<GoogleOAuthInitResponse> => {
    try {
      console.log('🔍 Initializing Google OAuth flow');
      const response = await fetch('/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Google OAuth initialized:', { hasUrl: !!data.authorizationUrl });

      return data;
    } catch (error) {
      console.error('❌ Google OAuth init error:', error);
      throw error;
    }
  },

  // Handle Google OAuth callback
  handleGoogleCallback: async (request: GoogleOAuthCallbackRequest): Promise<GoogleOAuthCallbackResponse> => {
    try {
      console.log('🔍 Handling Google OAuth callback');
      const response = await fetch('/api/auth/customer/google/callback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        // Get error details from response
        let errorDetails = '';
        try {
          const errorData = await response.json();
          errorDetails = JSON.stringify(errorData, null, 2);
          console.error('❌ OAuth callback API error details:', errorData);
        } catch (e) {
          errorDetails = await response.text();
          console.error('❌ OAuth callback API error (text):', errorDetails);
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}\nDetails: ${errorDetails}`);
      }

      const data = await response.json();
      console.log('✅ Google OAuth callback handled:', {
        success: data.success,
        isNewCustomer: data.isNewCustomer
      });

      return data;
    } catch (error) {
      console.error('❌ Google OAuth callback error:', error);
      throw error;
    }
  },

  // Sign in with credentials
  signIn: async (request: CustomerLoginRequest): Promise<CustomerLoginResponse> => {
    try {
      console.log('🔍 Signing in with credentials:', { email: request.email });
      const response = await fetch('/api/auth/customer/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Sign in successful:', { customerId: data.customer?.id });

      return data;
    } catch (error) {
      console.error('❌ Sign in error:', error);
      throw error;
    }
  },

  // Refresh access token
  refreshToken: async (request: TokenRefreshRequest): Promise<TokenRefreshResponse> => {
    try {
      console.log('🔍 Refreshing access token');
      const response = await fetch('/api/auth/customer/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Token refreshed successfully');

      return data;
    } catch (error) {
      console.error('❌ Token refresh error:', error);
      throw error;
    }
  },

  // Sign out
  signOut: async (): Promise<{ success: boolean }> => {
    try {
      console.log('🔍 Signing out');
      const response = await fetch('/api/auth/customer/logout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Sign out successful');

      return data;
    } catch (error) {
      console.error('❌ Sign out error:', error);
      throw error;
    }
  },

  // Get current customer profile
  getProfile: async (): Promise<Customer> => {
    try {
      console.log('🔍 Fetching customer profile');
      const response = await fetch('/api/auth/customer/profile', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Profile fetched:', { customerId: data.id });

      return data;
    } catch (error) {
      console.error('❌ Profile fetch error:', error);
      throw error;
    }
  },

  // Validate current session
  validateSession: async (): Promise<{ valid: boolean; customer?: Customer }> => {
    try {
      console.log('🔍 Validating session');
      // Ambil accessToken dari tokenStorage
      const { tokenStorage } = await import('@/lib/utils/token-storage');
      const tokens = await tokenStorage.getTokens();
      const accessToken = tokens?.accessToken;

      const headers: Record<string, string> = { 'Content-Type': 'application/json' };
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      // Gunakan BACKEND_API_URL jika ada, fallback ke relative path
      const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3000';
      const url = `${BACKEND_API_URL}/api/auth/customer/validate`;

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        console.warn('[DEBUG] validateSession: response not ok', response.status, response.statusText);
        return { valid: false };
      }

      const data = await response.json();
      console.log('✅ Session validated:', { valid: data.valid });

      return data;
    } catch (error) {
      console.error('❌ Session validation error:', error);
      return { valid: false };
    }
  },
};