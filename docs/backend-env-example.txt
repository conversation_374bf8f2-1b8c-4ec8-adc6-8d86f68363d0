# ===================================
# BACKEND API ENVIRONMENT VARIABLES
# ===================================
# Copy this to your backend project as .env file

# ===================================
# SERVER CONFIGURATION
# ===================================
PORT=3000
NODE_ENV=development
API_VERSION=v1

# ===================================
# DATABASE CONFIGURATION
# ===================================
# PostgreSQL (recommended)
DATABASE_URL=postgresql://username:password@localhost:5432/wellness_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wellness_db
DB_USER=your_db_username
DB_PASSWORD=your_db_password
DB_SSL=false

# MySQL Alternative
# DATABASE_URL=mysql://username:password@localhost:3306/wellness_db

# MongoDB Alternative
# DATABASE_URL=mongodb://localhost:27017/wellness_db

# ===================================
# GOOGLE OAUTH CONFIGURATION
# ===================================
# Same credentials as frontend, but backend needs them for token verification
GOOGLE_CLIENT_ID=662198729563-8vq9koqqdued0adc2ua1l1hnv2nfmrsr.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Google OAuth endpoints
GOOGLE_TOKEN_URL=https://oauth2.googleapis.com/token
GOOGLE_USERINFO_URL=https://www.googleapis.com/oauth2/v2/userinfo

# ===================================
# JWT & SECURITY CONFIGURATION
# ===================================
# Must match frontend JWT_SECRET for token compatibility
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Key for frontend authentication
API_KEY=pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b

# CSRF Protection
CSRF_SECRET=your_csrf_secret_here

# Encryption keys
ENCRYPTION_KEY=your-32-character-encryption-key-here

# ===================================
# CORS CONFIGURATION
# ===================================
# Allow requests from frontend
CORS_ORIGIN=http://localhost:3002
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-CSRF-Token

# ===================================
# RATE LIMITING CONFIGURATION
# ===================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX=10
RATE_LIMIT_AUTH_WINDOW=900000

# ===================================
# SESSION CONFIGURATION
# ===================================
SESSION_SECRET=your-session-secret-here
SESSION_MAX_AGE=86400000
SESSION_SECURE=false
SESSION_HTTP_ONLY=true

# ===================================
# EMAIL CONFIGURATION (Optional)
# ===================================
# For sending verification emails, password resets, etc.
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# ===================================
# EXTERNAL SERVICES (Optional)
# ===================================
# Redis for caching and sessions
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File storage (AWS S3, Cloudinary, etc.)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=wellness-app-uploads

# Cloudinary for image uploads
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# ===================================
# LOGGING & MONITORING
# ===================================
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# Sentry for error monitoring
SENTRY_DSN=your_sentry_dsn_here

# ===================================
# PAYMENT INTEGRATION (Optional)
# ===================================
# Stripe for payments
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# ===================================
# TENANT CONFIGURATION
# ===================================
# Multi-tenant support
DEFAULT_TENANT_ID=1
TENANT_ISOLATION=true

# ===================================
# DEVELOPMENT TOOLS
# ===================================
# Enable debug mode
DEBUG=auth:*,api:*
VERBOSE_LOGGING=true

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH=/api-docs

# ===================================
# PRODUCTION OVERRIDES
# ===================================
# These should be different in production
# NODE_ENV=production
# DB_SSL=true
# SESSION_SECURE=true
# CORS_ORIGIN=https://yourdomain.com
# LOG_LEVEL=error
