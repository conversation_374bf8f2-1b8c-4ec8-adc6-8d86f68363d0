# 🔧 Google OAuth Setup - CRITICAL CONFIGURATION REQUIRED

## ⚠️ **URGENT: Google Cloud Console Configuration Missing**

**Current Issue:** Google OAuth failing with "invalid_grant" error because redirect U<PERSON> not configured in Google Cloud Console.

**Server Status:** ✅ Running on port 3002 (http://localhost:3002)
**Redirect URI Used:** `http://localhost:3002/auth/callback`

## 🔧 **REQUIRED Google Cloud Console Configuration:**

### 1. **Authorized JavaScript Origins** ⚠️ CRITICAL
Add this exact origin to your Google Cloud Console:

```
http://localhost:3002  ← **MUST ADD THIS**
```

### 2. **Authorized Redirect URIs** ⚠️ CRITICAL
Add this exact redirect URI to your Google Cloud Console:

```
http://localhost:3002/auth/callback  ← **MUST ADD THIS**
```

**Note:** The application uses `/auth/callback` (frontend page) NOT `/api/auth/customer/google/callback` (API route)

## 📝 **STEP-BY-STEP CONFIGURATION:**

### **Step 1: Open Google Cloud Console**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to **APIs & Services** > **Credentials**
4. Click on your OAuth 2.0 Client ID

### **Step 2: Add Authorized JavaScript Origins**
1. Scroll to **Authorized JavaScript origins**
2. Click **+ ADD URI**
3. Add: `http://localhost:3002`
4. Click **Save**

### **Step 3: Add Authorized Redirect URIs**
1. Scroll to **Authorized redirect URIs**
2. Click **+ ADD URI**
3. Add: `http://localhost:3002/auth/callback`
4. Click **Save**

### **Step 4: Verify Configuration**
Your Google Cloud Console should now have:
- **JavaScript Origins**: `http://localhost:3002`
- **Redirect URIs**: `http://localhost:3002/auth/callback`

## ✅ **Verification:**

Setelah update Google Cloud Console, restart development server:

```bash
# Stop current server (Ctrl+C)
# Then restart
npm run dev
```

Server akan otomatis mencari port yang available (3003, 3004, etc.)

## 🔍 **Testing OAuth Flow:**

1. **Open browser**: http://localhost:3003
2. **Click "Sign In with Google"**
3. **Check console logs** untuk environment configuration
4. **Verify redirect** ke Google OAuth page
5. **Complete authentication** dan check callback

## 🐛 **Debugging:**

Jika masih ada error, check console logs untuk:

```
🔧 Environment Configuration:
  App URL: http://localhost:3003
  Google Redirect URI: http://localhost:3003/auth/callback
  Current Port: 3003
```

## 📋 **Environment Variables yang Sudah Diupdate:**

```bash
# .env file sudah diupdate:
NEXT_PUBLIC_API_URL = http://localhost:3003
GOOGLE_REDIRECT_URI=http://localhost:3003/auth/callback
```

## 🚀 **Dynamic Port Detection:**

Aplikasi sekarang menggunakan dynamic port detection, jadi:
- ✅ Otomatis detect port yang sedang digunakan
- ✅ Generate correct redirect URIs
- ✅ Handle port changes tanpa manual configuration
- ✅ Log environment configuration untuk debugging

## 🔄 **Next Steps:**

1. **Update Google Cloud Console** (sesuai langkah di atas)
2. **Restart development server**
3. **Test Google OAuth authentication**
4. **Verify callback flow working**

---

## 📞 **Jika Masih Ada Masalah:**

1. **Check browser console** untuk error messages
2. **Check server logs** untuk OAuth initialization
3. **Verify Google Cloud Console** configuration
4. **Ensure environment variables** loaded correctly

**Common Issues:**
- ❌ **Port mismatch**: Google Cloud Console belum diupdate
- ❌ **CORS error**: JavaScript origins tidak sesuai
- ❌ **Callback error**: Redirect URI tidak match
- ❌ **Environment**: .env file tidak ter-reload

**Solutions:**
- ✅ **Add all ports** ke Google Cloud Console
- ✅ **Restart dev server** setelah update .env
- ✅ **Clear browser cache** jika perlu
- ✅ **Check network tab** untuk failed requests
