# Authentication System Documentation

## Overview

Sistem authentication yang telah diimplementasikan menggunakan Google OAuth 2.0 dengan FAANG-level security practices. Sistem ini menyediakan:

- ✅ Google OAuth 2.0 authentication
- ✅ Secure session management dengan JWT tokens
- ✅ Protected booking flow
- ✅ Rate limiting dan CSRF protection
- ✅ Automatic token refresh
- ✅ Responsive UI components

## Architecture

### 1. Authentication Flow

```
User clicks "Book Now" → Check auth status → If not authenticated:
  ↓
Google OAuth Init → User signs in → OAuth Callback → Store tokens → Redirect to booking
```

### 2. Key Components

#### Backend API Routes
- `/api/auth/customer/google/init` - Initialize Google OAuth
- `/api/auth/customer/google/callback` - Handle OAuth callback
- `/api/auth/customer/login` - Credentials-based login
- `/api/auth/customer/refresh` - Token refresh
- `/api/auth/customer/logout` - Session logout
- `/api/auth/customer/validate` - Session validation
- `/api/auth/customer/profile` - User profile

#### Frontend Components
- `AuthProvider` - React Context for auth state
- `UserProfile` - User profile dropdown
- `ProtectedRoute` - Route protection
- `AuthLoading` - Loading states
- `ErrorBoundary` - Error handling

#### Security Middleware
- `rate-limit.ts` - Rate limiting protection
- `csrf.ts` - CSRF token protection
- `token-storage.ts` - Secure token storage

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` dan isi:

```bash
# External API Configuration
EXTERNAL_API_BASE_URL=http://localhost:3000
EXTERNAL_API_KEY=your_external_api_key_here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:3002/auth/callback

# Security
JWT_SECRET=your_jwt_secret_here
CSRF_SECRET=your_csrf_secret_here
```

### 2. Google OAuth Setup

1. Buka [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project atau pilih existing project
3. Enable Google+ API
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:3002/auth/callback`
5. Copy Client ID dan Client Secret ke environment variables

### 3. External API Integration

Pastikan external API (port 3000) sudah running dan memiliki endpoints:
- `POST /api/auth/customer/login`
- `POST /api/auth/customer/google`
- `POST /api/auth/customer/refresh`
- `DELETE /api/auth/customer/logout`

## Usage Examples

### 1. Protecting Routes

```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

function BookingPage() {
  return (
    <ProtectedRoute>
      <BookingContent />
    </ProtectedRoute>
  );
}
```

### 2. Using Auth Context

```tsx
import { useAuth } from '@/lib/contexts/AuthContext';

function MyComponent() {
  const { isAuthenticated, customer, signOut } = useAuth();
  
  if (!isAuthenticated) {
    return <SignInPrompt />;
  }
  
  return <div>Welcome, {customer?.firstName}!</div>;
}
```

### 3. Protected Booking Flow

```tsx
import { useAuthForBooking } from '@/lib/hooks/useAuthentication';

function BookButton({ scheduleId }) {
  const { requireAuth } = useAuthForBooking();
  
  const handleBook = () => {
    const needsAuth = requireAuth(scheduleId);
    if (needsAuth) {
      // User will be redirected to sign in
      return;
    }
    
    // Proceed with booking
    bookClass(scheduleId);
  };
  
  return <Button onClick={handleBook}>Book Now</Button>;
}
```

## Security Features

### 1. Rate Limiting

- Authentication endpoints: 10 requests per 15 minutes
- OAuth endpoints: 5 requests per 5 minutes
- API endpoints: 100 requests per minute
- Public endpoints: 200 requests per minute

### 2. CSRF Protection

- Automatic CSRF token generation
- Token validation for state-changing requests
- Secure token storage in HTTP-only cookies

### 3. Token Security

- JWT tokens dengan expiration
- Automatic token refresh
- Secure storage dengan Web Crypto API
- Device fingerprinting untuk additional security

### 4. Error Handling

- Comprehensive error boundaries
- User-friendly error messages
- Automatic error reporting
- Graceful fallbacks

## Testing

### 1. Manual Testing

1. Start development server: `npm run dev`
2. Navigate to `http://localhost:3002`
3. Click "Book Now" pada schedule card
4. Verify redirect ke Google OAuth
5. Complete sign in process
6. Verify redirect back ke booking flow

### 2. Authentication States

Test berbagai authentication states:
- ✅ Unauthenticated user
- ✅ Authentication in progress
- ✅ Authenticated user
- ✅ Token refresh
- ✅ Sign out

### 3. Error Scenarios

Test error handling:
- ✅ Network errors
- ✅ Invalid tokens
- ✅ Rate limiting
- ✅ CSRF protection

## Troubleshooting

### Common Issues

1. **"Unauthorized" errors**
   - Check environment variables
   - Verify external API is running
   - Check API key validity

2. **OAuth redirect errors**
   - Verify Google OAuth configuration
   - Check redirect URI matches exactly
   - Ensure HTTPS in production

3. **Rate limiting issues**
   - Check rate limit headers in network tab
   - Adjust rate limit configs if needed
   - Clear browser storage

4. **CSRF token errors**
   - Check CSRF token in cookies
   - Verify token in request headers
   - Clear browser cookies

### Debug Mode

Enable debug logging dengan:

```bash
DEBUG=auth:* npm run dev
```

## Production Considerations

### 1. Security Checklist

- [ ] Use HTTPS untuk all authentication endpoints
- [ ] Set secure environment variables
- [ ] Enable CSRF protection
- [ ] Configure proper CORS headers
- [ ] Set up rate limiting
- [ ] Enable security headers

### 2. Monitoring

- Set up error monitoring (Sentry, LogRocket)
- Monitor authentication success rates
- Track rate limiting metrics
- Monitor token refresh patterns

### 3. Performance

- Implement Redis untuk rate limiting storage
- Use CDN untuk static assets
- Optimize token refresh timing
- Cache user profile data

## API Reference

Detailed API documentation tersedia di:
- [Authentication API](./API_AUTHENTICATION.md)
- [Error Codes](./ERROR_CODES.md)
- [Security Guidelines](./SECURITY.md)
